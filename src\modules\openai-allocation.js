/**
 * OpenAI Allocation System
 * Handles asset allocation suggestions using OpenAI API
 */
export class OpenAIAllocationSystem {
  constructor() {
    this.isInitialized = false;
    this.isGenerating = false;
    this.currentValue = 0;
    this.currentProfile = 'conservador';
    this.Button = null;
    this.Button1 = null;
    this.Button2 = null;
    this.Button3 = null;
    this.Button4 = null;
    this.Button5 = null;
    this.ButtonStop = null;
    this.ButtonSend = null;
    this.LoadingIndicator = null;
    this.SliderContainer = null;
    this.InputElement = null;
    this.ResultContainer = null;
    this.ResultText = null;
    this.PromptInput = null;
    this.ProcessPromptButton = null;
    this.patrimonySync = null;
  }

  init() {
    if (this.isInitialized) {
      return;
    }

    document.addEventListener('DOMContentLoaded', () => {
      this.initElements();
      this.setupEventListeners();
    });

    this.isInitialized = true;
  }

  initElements() {
    // Inicializa elementos DOM
    this.Button = document.querySelector('[data-button="1"]');
    this.Button1 = document.querySelector('[data-button="1"]');
    this.Button2 = document.querySelector('[data-button="2"]');
    this.Button3 = document.querySelector('[data-button="3"]');
    this.Button4 = document.querySelector('[data-button="4"]');
    this.Button5 = document.querySelector('[data-button="5"]');
    this.ButtonStop = document.querySelector('[data-button="stop"]');
    this.ButtonSend = document.querySelector('[data-button="send"]');
    this.LoadingIndicator = document.querySelector('[data-element="loading"]');
    this.SliderContainer = document.querySelector('.grupo_slider_system');
    this.InputElement = document.querySelector('.currency-input[is-main="true"]');
    this.ResultContainer = document.querySelector('[data-element="result"]');
    this.ResultText = document.querySelector('[data-element="result-text"]');
    this.PromptInput = document.querySelector('.prompt-input');
    this.ProcessPromptButton = document.querySelector('.process-prompt');

    // Estado inicial
    if (this.LoadingIndicator) {
      this.LoadingIndicator.style.display = 'none';
    }
    if (this.ButtonStop) {
      this.ButtonStop.style.display = 'none';
    }

    // Obtém referência ao sistema de patrimônio
    this.getPatrimonySync();

    // Expõe funções de debug em desenvolvimento
    this.exposeDebugFunctions();

    // Log de inicialização
    console.warn('🤖 OpenAI Allocation System inicializado');
    console.warn('💡 Use debugOpenAI.checkElements() para verificar integração');
  }

  setupEventListeners() {
    // Botões de perfil
    const profileButtons = [
      { button: this.Button1, profile: 'conservador' },
      { button: this.Button2, profile: 'moderado' },
      { button: this.Button3, profile: 'arrojado' },
      { button: this.Button4, profile: 'super-arrojado' },
      { button: this.Button5, profile: 'personalizado' },
    ];

    profileButtons.forEach(({ button, profile }) => {
      if (button) {
        button.addEventListener('click', () => {
          this.selectProfile(profile);
        });
      }
    });

    // Botão de envio
    if (this.ButtonSend) {
      this.ButtonSend.addEventListener('click', () => {
        this.generateAllocation();
      });
    }

    // Botão de parar
    if (this.ButtonStop) {
      this.ButtonStop.addEventListener('click', () => {
        this.stopGeneration();
      });
    }

    // Input de valor principal
    if (this.InputElement) {
      this.InputElement.addEventListener('input', () => {
        this.updateValue();
      });
    } else {
      // Fallback: tenta encontrar qualquer input principal
      const fallbackInput = document.querySelector(
        '#currency, [data-currency="true"][is-main="true"]'
      );
      if (fallbackInput) {
        this.InputElement = fallbackInput;
        this.InputElement.addEventListener('input', () => {
          this.updateValue();
        });
      }
    }

    // Prompt personalizado - botão process-prompt
    if (this.ProcessPromptButton) {
      this.ProcessPromptButton.addEventListener('click', () => {
        this.generateCustomAllocation();
      });
    }

    // Prompt personalizado - Enter key no input
    if (this.PromptInput) {
      this.PromptInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          this.generateCustomAllocation();
        }
      });
    }
  }

  selectProfile(profile) {
    this.currentProfile = profile;

    // Atualiza estado visual dos botões
    const buttons = [this.Button1, this.Button2, this.Button3, this.Button4, this.Button5];
    buttons.forEach((button) => {
      if (button) {
        button.classList.remove('is-selected');
      }
    });

    const selectedButton = document.querySelector(
      `[data-button="${this.getProfileNumber(profile)}"]`
    );
    if (selectedButton) {
      selectedButton.classList.add('is-selected');
    }
  }

  getProfileNumber(profile) {
    const profileMap = {
      conservador: '1',
      moderado: '2',
      arrojado: '3',
      'super-arrojado': '4',
      personalizado: '5',
    };
    return profileMap[profile] || '1';
  }

  updateValue() {
    if (this.InputElement) {
      // Usa o mesmo método de parsing do PatrimonySync para consistência
      this.currentValue = this.parseCurrencyValue(this.InputElement.value);
      console.warn(
        `💰 Valor atualizado do input principal: ${this.formatCurrency(this.currentValue)}`
      );
    }
  }

  async generateAllocation() {
    if (this.isGenerating) {
      return;
    }

    if (this.currentValue === 0) {
      console.warn('Por favor, insira um valor válido para o patrimônio.');
      return;
    }

    this.isGenerating = true;
    this.showLoading();

    try {
      const allocation = await this.callOpenAI();
      this.displayResult(allocation);
    } catch (error) {
      this.displayError(error.message);
    } finally {
      this.hideLoading();
      this.isGenerating = false;
    }
  }

  async generateCustomAllocation() {
    if (this.isGenerating) {
      return;
    }

    const customPrompt = this.PromptInput?.value?.trim();
    if (!customPrompt) {
      console.warn('Por favor, descreva como deseja alocar seu patrimônio.');
      return;
    }

    // O patrimônio SEMPRE deve estar definido no input principal
    const patrimonyValue = this.currentValue;

    console.warn(
      `💰 Valor do patrimônio (input principal): ${this.formatCurrency(patrimonyValue)}`
    );
    console.warn(`📝 Prompt de alocação: "${customPrompt}"`);

    // Valida se o patrimônio foi definido no input principal
    if (patrimonyValue <= 0) {
      this.showToast(
        '⚠️',
        'Por favor, defina seu patrimônio no campo principal antes de fazer alocações',
        'warning'
      );
      return;
    }

    console.warn(
      `✅ Processando alocação com patrimônio de: ${this.formatCurrency(patrimonyValue)}`
    );

    this.isGenerating = true;
    this.showLoading();

    try {
      const allocation = await this.callOpenAIWithCustomPrompt(customPrompt);
      this.displayResult(allocation);

      // Processa a alocação e atualiza os inputs
      await this.processAllocationResponse(allocation);

      // Mostra feedback visual
      this.showAllocationAppliedFeedback();

      // Limpa o input após processar
      if (this.PromptInput) {
        this.PromptInput.value = '';
      }
    } catch (error) {
      this.displayError(error.message);
    } finally {
      this.hideLoading();
      this.isGenerating = false;
    }
  }

  async callOpenAI() {
    const prompt = this.buildPrompt();

    const response = await window.fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.getAPIKey()}`,
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content:
              'Você é um consultor financeiro especializado em alocação de ativos no mercado brasileiro. Seja muito flexível com linguagem natural, informal e coloquial. Interprete a intenção do usuário mesmo com erros de gramática ou valores pequenos. Sempre forneça sugestões construtivas independente do valor mencionado.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 1500,
        temperature: 0.8,
        top_p: 0.9,
        frequency_penalty: 0.2,
      }),
    });

    if (!response.ok) {
      throw new Error(`Erro na API: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  async callOpenAIWithCustomPrompt(customPrompt) {
    const prompt = this.buildCustomPrompt(customPrompt);

    const response = await window.fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.getAPIKey()}`,
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content:
              'Você é um assistente de alocação financeira PRECISO e RESTRITIVO. EXECUTE APENAS o que for solicitado pelo usuário. NÃO sugira alocações adicionais. NÃO complete portfolios automaticamente. NÃO dê conselhos não solicitados. Seja direto, preciso e responda SOMENTE o que foi pedido. Use linguagem natural para interpretar, mas seja restritivo nas respostas.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 1500,
        temperature: 0.8,
        top_p: 0.9,
        frequency_penalty: 0.2,
        presence_penalty: 0.1,
      }),
    });

    if (!response.ok) {
      throw new Error(`Erro na API: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  async processAllocationResponse(allocationText) {
    try {
      // Extrai percentuais da resposta da IA
      const allocations = this.extractAllocationsFromText(allocationText);

      if (allocations.length > 0) {
        await this.updatePatrimonyItems(allocations);
        return true;
      }
      this.showNoAllocationFoundWarning();
      return false;
    } catch (error) {
      console.warn('Erro ao processar alocação automática:', error);
      this.showAllocationError(error.message);
      return false;
    }
  }

  extractAllocationsFromText(text) {
    console.warn('🔍 Iniciando extração de alocações do texto da IA...');
    console.warn('📝 Texto recebido:', text);

    const allocations = [];

    // Primeiro, tenta extrair percentuais diretamente do input do usuário (mais prioritário)
    const userPercentages = this.extractUserPercentages(text);
    if (userPercentages.length > 0) {
      console.warn(`📊 Percentuais específicos encontrados: ${userPercentages.length} itens`);
      console.warn('📊 Percentuais extraídos:', userPercentages);
      return userPercentages;
    }

    console.warn('🔄 Processando resposta da IA com padrões regex...');
    console.warn('📋 Texto a ser analisado:', text.substring(0, 200) + '...');

    // Preview dos termos que esperamos encontrar
    const expectedTerms = ['cdb', 'cri', 'cra', 'estruturada'];
    console.warn('🎯 Termos esperados neste prompt:', expectedTerms);
    expectedTerms.forEach((term) => {
      const found = text.toLowerCase().includes(term);
      console.warn(
        `   ${found ? '✅' : '❌'} "${term}": ${found ? 'encontrado' : 'NÃO encontrado'} no texto`
      );
    });

    // Se não encontrou percentuais diretos, processa resposta da IA
    const patterns = [
      // Padrão principal: "Categoria: X%" ou "Categoria X%"
      /(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)[\s\-:]*(\d+(?:[,.]\d+)?)\s*%/gi,
      // Padrão secundário: "X% para Categoria"
      /(\d+(?:[,.]\d+)?)\s*%\s*(?:para|em|de|do|da)?\s*(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)/gi,
      // Padrão bullet point: "• Categoria: X%"
      /[•\-*]\s*(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)[\s\-:]*(\d+(?:[,.]\d+)?)\s*%/gi,
      // Padrão para valores absolutos: "Categoria: R$ X" ou "- Categoria: R$ X"
      /[•\-*]?\s*(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)[\s\-:]*r\$\s*(\d+(?:[,.]\d+)*(?:[,.]\d{2})?)/gi,
      // Padrão para valores absolutos invertido: "R$ X em Categoria"
      /r\$\s*(\d+(?:[,.]\d+)*(?:[,.]\d{2})?)\s*(?:em|para|de|do|da)?\s*(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)/gi,
    ];

    patterns.forEach((pattern, patternIndex) => {
      console.warn(`🔍 Testando padrão ${patternIndex + 1}/${patterns.length}:`, pattern.source);

      // Reset pattern para evitar problemas de lastIndex
      pattern.lastIndex = 0;

      let match;
      let matchCount = 0;

      // Use uma abordagem mais robusta para capturar todos os matches
      const allMatches = [];
      while ((match = pattern.exec(text)) !== null) {
        allMatches.push([...match]);
        // Evita loop infinito com matches de length 0
        if (match.index === pattern.lastIndex) {
          pattern.lastIndex++;
        }
      }

      console.warn(`🎯 Padrão ${patternIndex + 1} encontrou ${allMatches.length} matches`);

      allMatches.forEach((match, matchIndex) => {
        matchCount++;
        console.warn(
          `🎯 Processando match ${matchCount}/${allMatches.length} do padrão ${patternIndex + 1}:`,
          {
            match0: match[0],
            match1: match[1],
            match2: match[2],
          }
        );

        let percentage, category, absoluteValue;

        // Verifica se é um padrão de valor absoluto (padrões 3 e 4)
        const isAbsolutePattern = patternIndex >= 3;

        if (isAbsolutePattern) {
          // Para padrões de valor absoluto
          if (match[1] && isNaN(match[1])) {
            // Categoria vem primeiro: "CDB: R$ 700,00"
            category = match[1].toLowerCase().trim();
            absoluteValue = this.parseValueCorrectly('R$ ' + match[2]);
          } else {
            // Valor vem primeiro: "R$ 700,00 em CDB"
            absoluteValue = this.parseValueCorrectly('R$ ' + match[1]);
            category = match[2].toLowerCase().trim();
          }

          // Converte valor absoluto para percentual
          const mainValue = this.currentValue;
          if (mainValue > 0) {
            percentage = (absoluteValue / mainValue) * 100;
          } else {
            percentage = 0;
          }

          console.warn(
            `💰 Encontrado valor absoluto: ${category} = R$ ${absoluteValue.toFixed(2)} (${percentage.toFixed(1)}%)`
          );
        } else {
          // Para padrões de percentual
          if (match[1] && isNaN(match[1])) {
            // Categoria vem primeiro
            category = match[1].toLowerCase().trim();
            percentage = parseFloat(match[2].replace(',', '.'));
          } else {
            // Percentual vem primeiro
            percentage = parseFloat(match[1].replace(',', '.'));
            category = match[2].toLowerCase().trim();
          }

          console.warn(`📊 Encontrado percentual: ${category} = ${percentage}%`);
        }

        if (percentage > 0 && percentage <= 100) {
          const matchedItem = this.findBestMatchingItem(category);

          if (matchedItem !== -1) {
            // Evita duplicatas
            const exists = allocations.find((a) => a.category === matchedItem);
            if (!exists) {
              console.warn(
                `✅ Mapeado para categoria ${matchedItem}: ${category} = ${percentage.toFixed(1)}%`
              );
              allocations.push({
                percentage: percentage,
                category: matchedItem,
                originalCategory: category,
              });
            } else {
              console.warn(`⚠️ Categoria ${matchedItem} já existe, ignorando duplicata`);
            }
          } else {
            console.warn(`⚠️ Categoria não mapeada: "${category}"`);
          }
        } else {
          console.warn(`⚠️ Percentual inválido: ${percentage}% para "${category}"`);
        }
      });

      console.warn(`📊 Padrão ${patternIndex + 1} processado: ${matchCount} matches encontrados`);
    });

    console.warn(`\n📋 RESUMO FINAL DE PROCESSAMENTO:`);
    console.warn(`   🔍 Padrões testados: ${patterns.length}`);
    console.warn(`   ✅ Alocações encontradas: ${allocations.length}`);
    console.warn(`   📊 Taxa de sucesso: ${allocations.length > 0 ? '✅ Sucesso' : '❌ Falha'}`);

    // Se não encontrou alocações estruturadas, avisa que não conseguiu interpretar
    if (allocations.length === 0) {
      console.warn('❌ Nenhuma alocação específica encontrada no texto da IA');
      console.warn('🔍 Texto analisado não contém padrões reconhecíveis');
    } else {
      console.warn(`✅ ALOCAÇÕES EXTRAÍDAS:`);
      allocations.forEach((alloc, index) => {
        console.warn(
          `   ${index + 1}. ${alloc.originalCategory} → Item ${alloc.category} = ${alloc.percentage}%`
        );
      });
    }

    return allocations;
  }

  extractUserPercentages(text) {
    const allocations = [];
    const normalizedText = text.toLowerCase().trim();

    // Padrões específicos para entrada do usuário com percentuais
    const userPatterns = [
      // "tenho 30% em cdb", "quero 50% cdb"
      /(?:tenho|quero|coloco|ponho|aplico|invisto|aloco)\s*(\d+(?:[,.]\d+)?)\s*%\s*(?:em|no|na|de|do|da)?\s*(cdb|lci|lca|tesouro|títulos?\s*públicos?|cri|cra|debêntures?|ações|estruturada?|fundos?)/gi,
      // "30% em cdb", "50% cdb"
      /(\d+(?:[,.]\d+)?)\s*%\s*(?:em|no|na|de|do|da|para)?\s*(cdb|lci|lca|tesouro|títulos?\s*públicos?|cri|cra|debêntures?|ações|estruturada?|fundos?)/gi,
      // "cdb 30%", "ações 50%"
      /(cdb|lci|lca|tesouro|títulos?\s*públicos?|cri|cra|debêntures?|ações|estruturada?|fundos?)\s*(\d+(?:[,.]\d+)?)\s*%/gi,
    ];

    userPatterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(normalizedText)) !== null) {
        let percentage, category;

        if (isNaN(match[1])) {
          // Categoria vem primeiro: "cdb 30%"
          category = match[1].toLowerCase().trim();
          percentage = parseFloat(match[2].replace(',', '.'));
        } else {
          // Percentual vem primeiro: "30% em cdb"
          percentage = parseFloat(match[1].replace(',', '.'));
          category = match[2].toLowerCase().trim();
        }

        if (percentage > 0 && percentage <= 100) {
          const matchedItem = this.findBestMatchingItem(category);

          if (matchedItem !== -1) {
            // Evita duplicatas
            const exists = allocations.find((a) => a.category === matchedItem);
            if (!exists) {
              allocations.push({
                percentage: percentage,
                category: matchedItem,
                originalCategory: category,
                isDirectUserInput: true, // Marca como entrada direta do usuário
              });
              console.warn(
                `📊 Percentual direto: ${percentage}% em ${category} → item[${matchedItem}]`
              );
            }
          } else {
            console.warn(`⚠️ Categoria não mapeada (input usuário): "${category}"`);
          }
        }
      }
    });

    return allocations;
  }

  findBestMatchingItem(category) {
    const normalizedCategory = category.toLowerCase().trim();
    const patrimonyItems = this.getPatrimonyItems();

    console.warn(`🔍 Procurando categoria: "${normalizedCategory}"`);
    console.warn(`📋 Total de itens disponíveis: ${patrimonyItems.length}`);

    // 🆕 DEBUGGING COMPLETO - Mostra todos os 15 itens com seus atributos
    console.warn('=== 📋 TODOS OS ITENS DISPONÍVEIS ===');
    patrimonyItems.forEach((item, index) => {
      if (item) {
        console.warn(
          `🔍 Item ${index}: { ativoProduct: "${item.ativoProduct}", ativoCategory: "${item.ativoCategory}" }`
        );
      } else {
        console.warn(`🔍 Item ${index}: ITEM VAZIO`);
      }
    });
    console.warn('=== 📋 FIM DA LISTA DE ITENS ===');

    let bestMatch = -1;
    let bestScore = 0;

    patrimonyItems.forEach((item, index) => {
      if (!item) return;

      const score = this.calculateMatchScore(normalizedCategory, item);
      console.warn(`📊 Item ${index} (${item.ativoProduct}): Score ${score}`);

      if (score > bestScore) {
        bestScore = score;
        bestMatch = index;
        console.warn(
          `🎯 NOVO MELHOR MATCH: Item ${index} (${item.ativoProduct}) com score ${score}`
        );
      }
    });

    // Se não encontrou match, usa mapeamento legacy
    if (bestMatch === -1) {
      console.warn(`⚠️ Nenhum match encontrado, usando mapeamento legacy...`);
      bestMatch = this.mapCategoryToIndexLegacy(normalizedCategory);
      console.warn(`📍 Mapeamento legacy retornou: ${bestMatch}`);
    } else {
      console.warn(
        `✅ Melhor match encontrado: Item ${bestMatch} (${patrimonyItems[bestMatch]?.ativoProduct}) com score ${bestScore}`
      );
    }

    return bestMatch;
  }

  calculateMatchScore(searchTerm, item) {
    let score = 0;

    // 🛠️ NORMALIZAÇÃO CONSISTENTE DE STRINGS
    const termo = searchTerm.toLowerCase().trim();
    const produto = item.ativoProduct.toLowerCase().trim();
    const categoria = item.ativoCategory.toLowerCase().trim();

    // 🆕 DEBUGGING MAIS DETALHADO
    console.warn(`🧮 CALCULANDO SCORE:`);
    console.warn(`   🎯 Termo de busca: "${termo}"`);
    console.warn(`   📦 Produto: "${produto}"`);
    console.warn(`   📂 Categoria: "${categoria}"`);

    // Score para correspondências exatas no produto (prioridade máxima)
    if (produto === termo) {
      score += 150;
      console.warn(`   ✅ Match EXATO no produto: +150 pontos (Total: ${score})`);
      return score; // Return early para matches exatos
    }

    // Score para correspondências exatas na categoria
    if (categoria === termo) {
      score += 120;
      console.warn(`   ✅ Match exato na categoria: +120 pontos (Total: ${score})`);
    }

    // Score para correspondências parciais no produto
    if (produto.includes(termo)) {
      score += 90;
      console.warn(`   ✅ Produto contém termo: +90 pontos (Total: ${score})`);
    }
    if (termo.includes(produto) && produto.length > 2) {
      score += 80;
      console.warn(`   ✅ Termo contém produto: +80 pontos (Total: ${score})`);
    }

    // Score para correspondências parciais na categoria
    if (categoria.includes(termo)) {
      score += 70;
      console.warn(`   ✅ Categoria contém termo: +70 pontos (Total: ${score})`);
    }
    if (termo.includes(categoria) && categoria.length > 2) {
      score += 60;
      console.warn(`   ✅ Termo contém categoria: +60 pontos (Total: ${score})`);
    }

    // Scores específicos para termos conhecidos (todos normalizados)
    const specificMatches = {
      cdb: ['cdb', 'renda fixa'],
      lci: ['cdb', 'lci', 'renda fixa'],
      lca: ['cdb', 'lca', 'renda fixa'],
      tesouro: ['títulos públicos', 'tesouro', 'renda fixa'],
      'títulos públicos': ['títulos públicos', 'renda fixa'],
      cri: ['cri', 'renda fixa'],
      cra: ['cri', 'cra', 'renda fixa'],
      debenture: ['cri', 'renda fixa'],
      debênture: ['cri', 'renda fixa'],
      ações: ['ações', 'renda variável'],
      estruturada: ['estruturada', 'renda variável'],
      'renda fixa': ['renda fixa'],
      'renda variável': ['renda variável'],
      'fundo de investimento': ['fundo de investimento'],
      outros: ['outros'],
    };

    if (specificMatches[termo]) {
      console.warn(`   🎯 Checando matches específicos para "${termo}":`, specificMatches[termo]);
      specificMatches[termo].forEach((termMatch) => {
        // Match exato no produto tem prioridade máxima
        if (produto === termMatch) {
          score += 100;
          console.warn(
            `   ✅ Match EXATO no produto (${termMatch}): +100 pontos (Total: ${score})`
          );
        } else if (produto.includes(termMatch)) {
          score += 80;
          console.warn(
            `   ✅ Match parcial no produto (${termMatch}): +80 pontos (Total: ${score})`
          );
        } else if (categoria.includes(termMatch)) {
          score += 60;
          console.warn(
            `   ✅ Match específico na categoria (${termMatch}): +60 pontos (Total: ${score})`
          );
        }
      });
    }

    console.warn(`📊 SCORE FINAL: ${score}`);

    // Debug adicional para casos problemáticos
    if (score === 0 && termo.length > 0) {
      console.warn(`⚠️ Score zero detectado para termo "${termo}" vs produto "${produto}"`);
    }

    return score;
  }

  mapCategoryToIndexLegacy(category) {
    // Fallback para o sistema antigo
    const categoryMap = {
      'renda fixa': 0,
      cdb: 0,
      lci: 0,
      lca: 0,
      tesouro: 1,
      'títulos públicos': 1,
      cri: 2,
      cra: 2,
      debenture: 2,
      fundo: 3,
      ações: 6,
      estruturada: 7,
      outros: 9,
    };

    return categoryMap[category] || 0;
  }

  async updatePatrimonyItems(allocations) {
    console.warn('🔄 OpenAI: Iniciando updatePatrimonyItems com', allocations.length, 'alocações');

    if (!this.patrimonySync) {
      this.getPatrimonySync();
    }

    // Tenta reconectar se necessário
    if (!this.patrimonySync) {
      this.attemptConnection();
    }

    // Continua mesmo sem patrimonySync, pois podemos atualizar diretamente no DOM
    if (!this.patrimonySync) {
      console.warn('❌ Sistema de patrimônio não conectado, aplicando diretamente no DOM');
    } else {
      console.warn('✅ Sistema de patrimônio conectado');
    }

    const mainValue = this.currentValue;
    if (mainValue <= 0) {
      console.warn('❌ Valor principal deve ser maior que zero');
      return;
    }

    console.warn('💰 Valor principal:', mainValue);

    // Obtém os itens do sistema de patrimônio
    const patrimonyItems = this.getPatrimonyItems();
    console.warn('📊 Itens de patrimônio encontrados:', patrimonyItems.length);

    // Primeiro, zera todos os valores para começar limpo
    await this.clearAllAllocations(patrimonyItems);

    // Aplica as novas alocações com animação sequencial
    console.warn(`🚀 INICIANDO APLICAÇÃO DE ${allocations.length} ALOCAÇÕES:`);
    for (let i = 0; i < allocations.length; i++) {
      const { percentage, category, originalCategory } = allocations[i];
      const value = (mainValue * percentage) / 100;

      console.warn(`💫 Aplicando alocação ${i + 1}/${allocations.length}:`, {
        categoriaOriginal: originalCategory || 'N/A',
        categoriaMapeada: category,
        porcentagem: percentage + '%',
        valorCalculado: value,
        valorFormatado: this.formatCurrency(value),
        item: patrimonyItems[category] ? 'encontrado' : 'não encontrado',
        produtoTarget: patrimonyItems[category]?.ativoProduct || 'N/A',
      });

      if (patrimonyItems[category]) {
        console.warn(
          `📝 Definindo Item ${category} (${patrimonyItems[category].ativoProduct}) = ${this.formatCurrency(value)}`
        );
        await this.setItemValueAnimated(patrimonyItems[category], value, percentage, i * 100);
        console.warn(`✅ Item ${category} aplicado com sucesso`);
      } else {
        console.warn(`❌ Item ${category} não encontrado - alocação ignorada`);
      }
    }
    console.warn(`🎯 APLICAÇÃO COMPLETA: ${allocations.length} alocações processadas`);

    console.warn('🔥 Disparando evento de sincronização...');
    // Dispara evento para atualizar totais
    this.triggerPatrimonyUpdate();
  }

  getPatrimonySync() {
    // Sistema de reconexão automática
    this.attemptConnection();

    // Inicia verificação contínua
    this.startConnectionMonitoring();
  }

  attemptConnection() {
    if (window.ReinoCalculator && window.ReinoCalculator.systems) {
      const module = window.ReinoCalculator.systems.patrimonySync;
      if (module && !this.patrimonySync) {
        this.patrimonySync = module;
        console.warn('✅ Sistema de patrimônio conectado');
        return true;
      }
    }
    return false;
  }

  startConnectionMonitoring() {
    let attempts = 0;
    const maxAttempts = 10;
    const interval = 500;

    const monitor = setInterval(() => {
      attempts += 1;

      if (this.patrimonySync) {
        clearInterval(monitor);
        console.warn('🔗 Monitoramento de conexão finalizado - Sistema conectado');
        return;
      }

      if (this.attemptConnection()) {
        clearInterval(monitor);
        return;
      }

      if (attempts >= maxAttempts) {
        clearInterval(monitor);
        console.warn('⚠️ Sistema de patrimônio não encontrado após múltiplas tentativas');
        console.warn('💡 Use debugOpenAI.checkElements() para diagnóstico');
      }
    }, interval);
  }

  getPatrimonyItems() {
    const items = [];
    const containers = document.querySelectorAll('.patrimonio_interactive_item');

    containers.forEach((container, index) => {
      // Busca elementos do estado ativo
      const activeItem = container.querySelector('.active-produto-item');
      const disabledItem = container.querySelector('.disabled-produto-item');

      // Busca especificamente por input com input-settings="receive"
      const input = container.querySelector('input[input-settings="receive"]');
      const slider = container.querySelector('range-slider');
      const percentageDisplay = container.querySelector('.porcentagem-calculadora');

      // Busca elementos do estado desabilitado (necessários para sincronização)
      const valorProduto = disabledItem?.querySelector('.valor-produto');
      const percentageDisabled = disabledItem?.querySelector('.porcentagem-calculadora-disabled');
      const backgroundItemAcao = disabledItem?.querySelector('.background-item-acao');

      // Extrai atributos de identificação
      const ativoProduct = container.getAttribute('ativo-product');
      const ativoCategory = container.getAttribute('ativo-category');

      // Fallback para texto interno se não tiver atributos
      const categoryText = container.querySelector('.categoria-ativo')?.textContent?.trim();
      const productText = container
        .querySelector('.ativo_alocated_top-wrapper > div:nth-child(2)')
        ?.textContent?.trim();

      if (input && slider) {
        items[index] = {
          container,
          activeItem,
          disabledItem,
          input,
          slider,
          percentageDisplay,
          valorProduto,
          percentageDisabled,
          backgroundItemAcao,
          index,
          value: 0,
          percentage: 0,
          maxAllowed: 0,
          ativoProduct: ativoProduct || productText || '',
          ativoCategory: ativoCategory || categoryText || '',
        };

        // Debug logging para verificar elementos encontrados
        console.warn(`🔍 Item ${index} elementos:`, {
          input: !!input,
          slider: !!slider,
          percentageDisplay: !!percentageDisplay,
          valorProduto: !!valorProduto,
          percentageDisabled: !!percentageDisabled,
          backgroundItemAcao: !!backgroundItemAcao,
        });
      }
    });

    return items;
  }

  async clearAllAllocations(patrimonyItems) {
    const promises = patrimonyItems.map((item, index) => {
      if (item) {
        return this.setItemValueAnimated(item, 0, 0, index * 50);
      }
    });

    await Promise.all(promises.filter(Boolean));
  }

  async setItemValueAnimated(item, value, percentage, delay = 0) {
    if (!item || !item.input || !item.slider) return;

    return new Promise((resolve) => {
      setTimeout(() => {
        try {
          // Adiciona classe de highlight
          item.container.classList.add('ai-updating');

          // Atualiza o input com valor formatado
          const formattedValue = new Intl.NumberFormat('pt-BR', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(value);

          console.warn(`🔍 DEBUG setItemValueAnimated - Item ${item.index}:`, {
            produto: item.ativoProduct || 'N/A',
            valorOriginal: value,
            valorFormatado: formattedValue,
            porcentagem: percentage,
            patrimonioTotal: this.currentValue,
            timestampAplicacao: new Date().toLocaleTimeString(),
          });

          item.input.value = formattedValue;

          // Atualiza o slider (valor entre 0 e 1)
          const sliderValue = Math.min(percentage / 100, 1);
          item.slider.value = sliderValue;

          // Atualiza o display de porcentagem (estado ativo)
          if (item.percentageDisplay) {
            item.percentageDisplay.textContent = `${percentage.toFixed(1)}%`;
            console.warn(
              `📊 Atualizou percentageDisplay ${item.index}:`,
              `${percentage.toFixed(1)}%`
            );
          } else {
            console.warn(`❌ percentageDisplay não encontrado para item ${item.index}`);
          }

          // Atualiza o display de porcentagem (estado desabilitado)
          if (item.percentageDisabled) {
            item.percentageDisabled.textContent = `${percentage.toFixed(1)}%`;
            console.warn(
              `📊 Atualizou percentageDisabled ${item.index}:`,
              `${percentage.toFixed(1)}%`
            );
          } else {
            console.warn(`❌ percentageDisabled não encontrado para item ${item.index}`);
          }

          // Atualiza o valor do produto (estado desabilitado)
          if (item.valorProduto) {
            item.valorProduto.textContent = formattedValue;
            console.warn(`💰 Atualizou valorProduto ${item.index}:`, formattedValue);
          } else {
            console.warn(`❌ valorProduto não encontrado para item ${item.index}`);
          }

          console.warn(`🎯 Disparando eventos para item ${item.index}:`, {
            valor: value,
            valorFormatado: formattedValue,
            porcentagem: percentage + '%',
          });

          // Aguarda um momento para garantir que o valor foi definido
          setTimeout(() => {
            // Dispara eventos de mudança para sincronizar com outros sistemas
            const inputEvent = new Event('input', { bubbles: true });
            const changeEvent = new Event('change', { bubbles: true });

            // Adiciona evento currencyChange para integração com PatrimonySync
            const currencyChangeEvent = new CustomEvent('currencyChange', {
              detail: {
                value: value,
                formatted: formattedValue,
              },
              bubbles: true,
            });

            item.input.dispatchEvent(inputEvent);
            item.input.dispatchEvent(changeEvent);
            item.input.dispatchEvent(currencyChangeEvent);
            item.slider.dispatchEvent(inputEvent);
            item.slider.dispatchEvent(changeEvent);

            console.warn(`✅ Eventos disparados para item ${item.index}`);
          }, 5);

          // Remove classe de highlight após animação
          setTimeout(() => {
            item.container.classList.remove('ai-updating');
            resolve();
          }, 300);
        } catch (error) {
          console.warn('Erro ao atualizar item patrimonial:', error);
          resolve();
        }
      }, delay);
    });
  }

  setItemValue(item, value, percentage) {
    return this.setItemValueAnimated(item, value, percentage, 0);
  }

  triggerPatrimonyUpdate() {
    console.warn('🚀 Disparando eventos de sincronização...');

    // Dispara evento customizado para notificar outros sistemas
    const event = new CustomEvent('aiAllocationUpdate', {
      detail: {
        source: 'openai',
        timestamp: Date.now(),
      },
    });

    document.dispatchEvent(event);
    console.warn('✅ Evento aiAllocationUpdate disparado');

    // Tenta sincronizar com AllocationSync diretamente
    try {
      console.warn('🔄 Tentando acessar AllocationSync...');

      if (typeof window.getAllocationSync === 'function') {
        const allocationSync = window.getAllocationSync();
        if (allocationSync && typeof allocationSync.updateAllAllocations === 'function') {
          console.warn('🔄 Chamando AllocationSync.updateAllAllocations()...');

          // Primeiro, atualiza os valores internos dos items do AllocationSync
          if (allocationSync.items && Array.isArray(allocationSync.items)) {
            console.warn('🔄 Atualizando valores internos do AllocationSync...');
            console.warn('🔍 Total de items do AllocationSync:', allocationSync.items.length);

            allocationSync.items.forEach((allocationItem, index) => {
              if (allocationItem && allocationItem.input) {
                // Força o AllocationSync a re-processar o valor do input
                const inputEvent = new Event('input', { bubbles: true });
                allocationItem.input.dispatchEvent(inputEvent);

                // Aguarda um tick para o evento ser processado
                setTimeout(() => {
                  console.warn(`📝 Item ${index} - Valor re-sincronizado:`, {
                    valorInput: allocationItem.input.value,
                    valorInterno: allocationItem.value,
                    porcentagem: allocationItem.percentage?.toFixed(1) + '%',
                  });
                }, 10);
              } else {
                console.warn(`❌ Item ${index} do AllocationSync sem input válido`);
              }
            });
          } else {
            console.warn('❌ AllocationSync.items não é um array válido');
          }

          // Aguarda os eventos de input serem processados antes de chamar updateAllAllocations
          setTimeout(() => {
            allocationSync.updateAllAllocations();

            // Força atualização dos displays de patrimônio restante
            if (
              typeof window.ReinoCalculator !== 'undefined' &&
              window.ReinoCalculator.systems &&
              window.ReinoCalculator.systems.patrimonySync
            ) {
              const patrimonySync = window.ReinoCalculator.systems.patrimonySync;
              if (typeof patrimonySync.updateWebflowPatrimonyDisplay === 'function') {
                patrimonySync.updateWebflowPatrimonyDisplay();
                console.warn('✅ Display de patrimônio restante atualizado');
              }
            }

            console.warn('✅ Sincronização completa realizada');
          }, 50);

          console.warn('✅ Sincronização com AllocationSync realizada');
        } else {
          console.warn('❌ AllocationSync.updateAllAllocations não disponível');
        }
      } else {
        console.warn('❌ window.getAllocationSync não disponível');
      }
    } catch (error) {
      console.warn('❌ Erro ao sincronizar com AllocationSync:', error);
    }
  }

  showAllocationAppliedFeedback() {
    // Cria um toast de feedback
    const toast = document.createElement('div');
    toast.className = 'ai-allocation-toast';
    toast.innerHTML = `
      <div class="toast-content">
        <div class="toast-icon">✅</div>
        <div class="toast-message">Alocação aplicada com sucesso!</div>
      </div>
    `;

    // Adiciona estilos inline para o toast
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      animation: slideInRight 0.3s ease-out forwards;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      max-width: 300px;
    `;

    // Adiciona animação CSS
    if (!document.getElementById('ai-toast-styles')) {
      const style = document.createElement('style');
      style.id = 'ai-toast-styles';
      style.textContent = `
        @keyframes slideInRight {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
        .toast-content {
          display: flex;
          align-items: center;
          gap: 10px;
        }
        .toast-icon {
          font-size: 16px;
        }
        .ai-updating {
          background: linear-gradient(90deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1));
          background-size: 200% 100%;
          animation: aiUpdatePulse 1s ease-in-out;
          border: 2px solid rgba(76, 175, 80, 0.3);
          border-radius: 8px;
        }
        @keyframes aiUpdatePulse {
          0% { background-position: 200% 0; }
          100% { background-position: -200% 0; }
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(toast);

    // Remove o toast após 3 segundos
    setTimeout(() => {
      toast.style.animation = 'slideOutRight 0.3s ease-in forwards';
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 3000);
  }

  showNoAllocationFoundWarning() {
    this.showToast('⚠️', 'Não foi possível extrair alocações do texto da IA', 'warning');
  }

  showAllocationError(message) {
    this.showToast('❌', `Erro ao aplicar alocação: ${message}`, 'error');
  }

  showToast(icon, message, type = 'success') {
    const colors = {
      success: { bg: 'linear-gradient(135deg, #4CAF50, #45a049)', border: '#4CAF50' },
      warning: { bg: 'linear-gradient(135deg, #FF9800, #F57C00)', border: '#FF9800' },
      error: { bg: 'linear-gradient(135deg, #f44336, #d32f2f)', border: '#f44336' },
    };

    const toast = document.createElement('div');
    toast.innerHTML = `
      <div class="toast-content">
        <div class="toast-icon">${icon}</div>
        <div class="toast-message">${message}</div>
      </div>
    `;

    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${colors[type].bg};
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      animation: slideInRight 0.3s ease-out forwards;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      max-width: 350px;
    `;

    document.body.appendChild(toast);

    setTimeout(
      () => {
        toast.style.animation = 'slideOutRight 0.3s ease-in forwards';
        setTimeout(() => {
          if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
          }
        }, 300);
      },
      type === 'error' ? 5000 : 3000
    );
  }

  buildPrompt() {
    const profileDescriptions = {
      conservador: 'perfil conservador (baixo risco, foco em renda fixa)',
      moderado: 'perfil moderado (risco equilibrado entre renda fixa e variável)',
      arrojado: 'perfil arrojado (maior exposição à renda variável)',
      'super-arrojado': 'perfil super arrojado (alta exposição a ativos de risco)',
      personalizado: 'perfil personalizado',
    };

    return `
      Preciso de uma sugestão de alocação de ativos para um investidor com ${profileDescriptions[this.currentProfile]}
      e patrimônio total de R$ ${this.formatCurrency(this.currentValue)}.

      Por favor, forneça:
      1. Percentuais específicos para cada classe de ativo
      2. Sugestão de produtos/fundos para cada categoria
      3. Justificativa da alocação baseada no perfil

      Considere o cenário econômico atual do Brasil e as melhores práticas de diversificação.

      Formato da resposta: estruturado e profissional, adequado para apresentação ao cliente.
    `;
  }

  hasUserPercentages(text) {
    const normalizedText = text.toLowerCase();
    // Verifica se o usuário mencionou percentuais específicos
    const percentagePattern = /\d+(?:[,.]\d+)?\s*%/;

    // Também detecta valores absolutos como "20 reais", "R$ 100"
    const absoluteValuePattern = /(?:r\$|reais?)\s*\d+|^\d+\s*(?:reais?|r\$)/i;

    // Detecta comandos específicos como "aloque X em Y"
    const specificCommandPattern = /aloque?|coloque?|invista|ponha|destine/i;

    return (
      percentagePattern.test(normalizedText) ||
      absoluteValuePattern.test(normalizedText) ||
      specificCommandPattern.test(normalizedText)
    );
  }

  buildCustomPrompt(customPrompt) {
    // Sempre usa o valor do input principal - prompts são SEMPRE para alocação
    const patrimonyValue = this.currentValue;

    // Detecta se o usuário mencionou valores absolutos (R$)
    const hasAbsoluteValues =
      /(?:r\$|reais?)\s*\d+/i.test(customPrompt) || /\d+\s*(?:reais?|r\$)/i.test(customPrompt);

    // Detecta produtos específicos mencionados
    const specificProducts = [];
    if (/\bcdb\b/i.test(customPrompt)) specificProducts.push('CDB');
    if (/\bcri\b/i.test(customPrompt)) specificProducts.push('CRI');
    if (/\bcra\b/i.test(customPrompt)) specificProducts.push('CRA');
    if (/\blci\b/i.test(customPrompt)) specificProducts.push('LCI');
    if (/\blca\b/i.test(customPrompt)) specificProducts.push('LCA');
    if (/títulos públicos|tesouro/i.test(customPrompt)) specificProducts.push('Títulos Públicos');
    if (/ações/i.test(customPrompt)) specificProducts.push('Ações');
    if (/fiis?/i.test(customPrompt)) specificProducts.push('FIIs');
    if (/etfs?/i.test(customPrompt)) specificProducts.push('ETFs');

    // Detecta se é uma solicitação específica (não sugestão completa)
    const isSpecificRequest =
      this.hasUserPercentages(customPrompt) || hasAbsoluteValues || specificProducts.length > 0;

    return `
      Analise a seguinte solicitação de alocação de patrimônio do investidor:

      "${customPrompt}"

      O patrimônio total disponível para alocação é de R$ ${this.formatCurrency(patrimonyValue)}.

      REGRAS FUNDAMENTAIS:
      ${
        isSpecificRequest
          ? `- Esta é uma solicitação ESPECÍFICA. EXECUTE APENAS o que foi solicitado.
         - NÃO sugira alocações adicionais ou complete para 100% automaticamente.
         - NÃO crie uma estratégia completa de portfolio.
         - RESPONDA APENAS com os itens solicitados.`
          : `- Esta é uma solicitação geral. Você pode sugerir uma alocação completa.`
      }

      ${
        hasAbsoluteValues
          ? `- O usuário mencionou valores em REAIS. Calcule os percentuais corretos baseado no patrimônio total.
         - EXEMPLO: Se o usuário diz "50 reais em CDB" e o patrimônio é R$ 400,00, então CDB = 50/400 = 12.5%
         - SEMPRE calcule: (valor_solicitado / patrimônio_total) × 100 = percentual correto`
          : ''
      }

      ${
        specificProducts.length > 0
          ? `- PRODUTOS ESPECÍFICOS MENCIONADOS: ${specificProducts.join(', ')}
         - Use EXATAMENTE esses nomes de produtos na resposta
         - NÃO substitua por outros produtos similares`
          : ''
      }

      ${
        isSpecificRequest
          ? `FORMATO DE RESPOSTA PARA SOLICITAÇÃO ESPECÍFICA:

      **ALOCAÇÃO SOLICITADA:**
      [Apenas os itens solicitados pelo usuário]

      **RESUMO:**
      [Breve confirmação do que foi alocado]`
          : `FORMATO DE RESPOSTA PARA SOLICITAÇÃO GERAL:

      **ALOCAÇÃO SUGERIDA:**
      • Renda Fixa: X%
      • Fundos de Investimento: Y%
      • Renda Variável: Z%
      • Outros Investimentos: W%

      **DETALHAMENTO POR CATEGORIA:**
      1. **Renda Fixa (X%):**
         - CDB: A%
         - CRI: B%
         - Títulos Públicos: C%

      2. **Fundos de Investimento (Y%):**
         - Ações: D%
         - Renda Fixa: E%

      3. **Renda Variável (Z%):**
         - Ações: F%
         - Estruturada: G%

      4. **Outros Investimentos (W%):**
         - [Especificar conforme solicitação]: H%

      **JUSTIFICATIVA:**
      [Explicação da estratégia baseada na solicitação do cliente]`
      }

      IMPORTANTE:
      - Use SEMPRE percentuais numéricos precisos. Exemplo: "20%" não "cerca de 20%".
      - Use os termos EXATOS mencionados pelo usuário.
      - Para solicitações específicas: RESPONDA APENAS o que foi pedido.
      - NÃO adicione sugestões não solicitadas.
      - Seja direto e preciso.
    `;
  }

  extractPatrimonyValueFromText(text) {
    // FUNÇÃO DESABILITADA: Prompts são sempre para alocação, não para definir patrimônio
    // O patrimônio deve estar sempre definido no input principal (is-main="true")
    console.warn('🚫 Extração de patrimônio do texto desabilitada - use o input principal');
    return 0;
  }

  parseValueFromMatch(valueStr, fullMatch) {
    // Remove pontos e vírgulas para normalizar
    let cleanValue = valueStr.replace(/[.,]/g, '');
    let value = parseFloat(cleanValue);

    if (isNaN(value)) return 0;

    const fullMatchLower = fullMatch.toLowerCase();

    // Identifica multiplicadores
    if (fullMatchLower.includes('milhões') || fullMatchLower.includes('milhao')) {
      value = value * 1000000;
    } else if (fullMatchLower.includes('bilhões') || fullMatchLower.includes('bilhao')) {
      value = value * 1000000000;
    } else if (
      fullMatchLower.includes('mil') ||
      fullMatchLower.includes('k') ||
      fullMatchLower.includes('thousand')
    ) {
      value = value * 1000;
    } else {
      // Heurística: se o valor tem 1-3 dígitos, provavelmente está em milhares
      if (value >= 10 && value <= 999 && !fullMatchLower.includes('reais')) {
        value = value * 1000;
      }
      // Se tem vírgula/ponto no original, pode ser decimal
      if (valueStr.includes(',') || valueStr.includes('.')) {
        const parts = valueStr.split(/[.,]/);
        if (parts.length === 2 && parts[1].length === 2) {
          // Formato decimal: 100,50 = 100.50
          value = parseFloat(parts[0]) + parseFloat(parts[1]) / 100;
        }
      }
    }

    return value;
  }

  // REMOVIDO: Não devemos atualizar o input principal
  // O usuário define seu patrimônio no input principal, não devemos alterar

  formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  }

  parseCurrencyValue(value) {
    if (!value || typeof value !== 'string') return 0;

    console.warn(`🔍 DEBUG parseCurrencyValue:`, {
      valorOriginal: value,
      tipo: typeof value,
    });

    // Remove tudo exceto dígitos, vírgulas e pontos
    const step1 = value.replace(/[^\d.,]/g, '');
    console.warn(`🔍 Após remover caracteres especiais: "${step1}"`);

    // Se tem ponto e vírgula, assume formato brasileiro (1.000,50)
    if (step1.includes('.') && step1.includes(',')) {
      const parts = step1.split(',');
      const integerPart = parts[0].replace(/\./g, ''); // Remove pontos dos milhares
      const decimalPart = parts[1] || '00';
      const result = parseFloat(`${integerPart}.${decimalPart}`);
      console.warn(`🔍 Formato brasileiro detectado: ${result}`);
      return result || 0;
    }

    // Se tem apenas vírgula, substitui por ponto
    const cleanValue = step1.replace(',', '.');
    const result = parseFloat(cleanValue) || 0;

    console.warn(`🔍 Valor final parseado: ${result}`);
    return result;
  }

  parseValueCorrectly(value) {
    if (!value || typeof value !== 'string') return 0;

    // Remove espaços e caracteres especiais, mantendo apenas números, vírgulas e pontos
    const cleaned = value.replace(/[^\d.,]/g, '');

    // Se contém ponto e vírgula (formato brasileiro: 1.000,50)
    if (cleaned.includes('.') && cleaned.includes(',')) {
      const lastCommaIndex = cleaned.lastIndexOf(',');
      const integerPart = cleaned.substring(0, lastCommaIndex).replace(/\./g, '');
      const decimalPart = cleaned.substring(lastCommaIndex + 1);
      return parseFloat(`${integerPart}.${decimalPart}`) || 0;
    }

    // Se contém apenas vírgula (formato: 1000,50)
    if (cleaned.includes(',') && !cleaned.includes('.')) {
      return parseFloat(cleaned.replace(',', '.')) || 0;
    }

    // Se contém apenas ponto
    if (cleaned.includes('.') && !cleaned.includes(',')) {
      // Se o ponto está nas últimas 2 casas, é decimal
      const parts = cleaned.split('.');
      if (parts.length === 2 && parts[1].length <= 2) {
        return parseFloat(cleaned) || 0;
      } else {
        // Senão, são separadores de milhares
        return parseFloat(cleaned.replace(/\./g, '')) || 0;
      }
    }

    // Apenas números
    return parseFloat(cleaned) || 0;
  }

  getAPIKey() {
    // Em produção, esta chave deve vir de variáveis de ambiente seguras
    return (
      window.process?.env?.OPENAI_API_KEY ||
      '********************************************************************************************************************************************************************'
    );
  }

  showLoading() {
    if (this.LoadingIndicator) {
      this.LoadingIndicator.style.display = 'block';
    }
    if (this.ButtonSend) {
      this.ButtonSend.style.display = 'none';
    }
    if (this.ButtonStop) {
      this.ButtonStop.style.display = 'block';
    }
    if (this.ResultContainer) {
      this.ResultContainer.style.display = 'none';
    }

    // Desabilita prompt input e botão durante loading
    if (this.PromptInput) {
      this.PromptInput.disabled = true;
      this.PromptInput.style.opacity = '0.6';
      this.PromptInput.style.cursor = 'not-allowed';
    }
    if (this.ProcessPromptButton) {
      this.ProcessPromptButton.disabled = true;
      this.ProcessPromptButton.style.opacity = '0.6';
      this.ProcessPromptButton.style.cursor = 'not-allowed';
      this.ProcessPromptButton.innerHTML = '<div>Processando...</div>';
    }
  }

  hideLoading() {
    if (this.LoadingIndicator) {
      this.LoadingIndicator.style.display = 'none';
    }
    if (this.ButtonSend) {
      this.ButtonSend.style.display = 'block';
    }
    if (this.ButtonStop) {
      this.ButtonStop.style.display = 'none';
    }

    // Reabilita prompt input e botão após loading
    if (this.PromptInput) {
      this.PromptInput.disabled = false;
      this.PromptInput.style.opacity = '1';
      this.PromptInput.style.cursor = 'text';
    }
    if (this.ProcessPromptButton) {
      this.ProcessPromptButton.disabled = false;
      this.ProcessPromptButton.style.opacity = '1';
      this.ProcessPromptButton.style.cursor = 'pointer';
      this.ProcessPromptButton.innerHTML = '<div>Alocar</div>';
    }
  }

  displayResult(allocation) {
    if (this.ResultText) {
      this.ResultText.innerHTML = this.formatAllocationText(allocation);
    }
    if (this.ResultContainer) {
      this.ResultContainer.style.display = 'block';
    }
  }

  displayError(errorMessage) {
    if (this.ResultText) {
      this.ResultText.innerHTML = `
        <div style="color: #e74c3c; padding: 20px; border-radius: 8px; background: #fdf2f2;">
          <h4>Erro na geração da alocação</h4>
          <p>${errorMessage}</p>
          <p>Por favor, tente novamente ou entre em contato com o suporte.</p>
        </div>
      `;
    }
    if (this.ResultContainer) {
      this.ResultContainer.style.display = 'block';
    }
  }

  formatAllocationText(text) {
    // Formata o texto da alocação com HTML adequado
    return text
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^\s*/, '<p>')
      .replace(/\s*$/, '</p>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>');
  }

  stopGeneration() {
    this.isGenerating = false;
    this.hideLoading();

    // Limpa o input se houver algum processo de prompt personalizado
    if (this.PromptInput) {
      // Não limpa automaticamente, deixa o usuário decidir
      this.PromptInput.focus();
    }
  }

  // Método público para atualizar valor externamente
  updateCurrentValue(value) {
    this.currentValue = value;
  }

  // Método público para obter perfil atual
  getCurrentProfile() {
    return this.currentProfile;
  }

  // Método público para obter valor atual
  getCurrentValue() {
    return this.currentValue;
  }

  // Função de debug para visualizar mapeamento
  debugPatrimonyMapping() {
    const items = this.getPatrimonyItems();
    console.warn('=== MAPEAMENTO DE ITENS PATRIMONIAIS ===');

    items.forEach((item, index) => {
      if (item) {
        console.warn(`[${index}] ${item.ativoCategory} - ${item.ativoProduct}`);
        console.warn(`    Container:`, item.container);
        console.warn(`    Atributos:`, {
          'ativo-product': item.ativoProduct,
          'ativo-category': item.ativoCategory,
        });
        console.warn('---');
      } else {
        console.warn(`[${index}] ITEM VAZIO`);
      }
    });

    console.warn('=== FIM DO MAPEAMENTO ===');
  }

  // Função para testar correspondência de categorias
  testCategoryMapping(searchTerm) {
    const items = this.getPatrimonyItems();
    console.warn(`=== TESTE DE CORRESPONDÊNCIA: "${searchTerm}" ===`);

    items.forEach((item, index) => {
      if (item) {
        const score = this.calculateMatchScore(searchTerm.toLowerCase(), item);
        console.warn(`[${index}] Score: ${score} - ${item.ativoCategory} - ${item.ativoProduct}`);
      }
    });

    const bestMatch = this.findBestMatchingItem(searchTerm);
    console.warn(`MELHOR CORRESPONDÊNCIA: Índice ${bestMatch}`);
    if (bestMatch !== -1 && items[bestMatch]) {
      console.warn(
        `ITEM SELECIONADO: ${items[bestMatch].ativoCategory} - ${items[bestMatch].ativoProduct}`
      );
    }
    console.warn('=== FIM DO TESTE ===');
  }

  // Função para testar extração de valores
  testValueExtraction(text) {
    console.warn(`=== TESTE DE EXTRAÇÃO DE VALOR: "${text}" ===`);
    const extractedValue = this.extractPatrimonyValueFromText(text);
    console.warn(`VALOR EXTRAÍDO: R$ ${extractedValue}`);
    console.warn(`VALOR FORMATADO: ${this.formatCurrency(extractedValue)}`);
    console.warn('=== FIM DO TESTE ===');
    return extractedValue;
  }

  // Função para testar extração de percentuais
  testPercentageExtraction(text) {
    console.warn(`=== TESTE DE EXTRAÇÃO DE PERCENTUAIS: "${text}" ===`);

    // Testa percentuais diretos do usuário
    const userPercentages = this.extractUserPercentages(text);
    console.warn(`PERCENTUAIS DIRETOS ENCONTRADOS: ${userPercentages.length}`);
    userPercentages.forEach((item, index) => {
      console.warn(
        `  [${index}] ${item.percentage}% → ${item.originalCategory} (item[${item.category}])`
      );
    });

    // Testa extração geral (incluindo resposta da IA)
    const allAllocations = this.extractAllocationsFromText(text);
    console.warn(`TOTAL DE ALOCAÇÕES: ${allAllocations.length}`);
    allAllocations.forEach((item, index) => {
      const type = item.isDirectUserInput ? 'DIRETO' : 'IA';
      console.warn(
        `  [${index}] ${item.percentage}% → ${item.originalCategory} (${type}) [item${item.category}]`
      );
    });

    // Verifica se tem percentuais
    const hasPercentages = this.hasUserPercentages(text);
    console.warn(`CONTÉM PERCENTUAIS: ${hasPercentages ? 'SIM' : 'NÃO'}`);

    console.warn('=== FIM DO TESTE ===');
    return allAllocations;
  }

  // Função para verificar se elementos estão sendo encontrados
  debugElementsCheck() {
    console.warn('=== VERIFICAÇÃO DE ELEMENTOS ===');

    // Verifica input principal (NÃO deve ser alterado)
    const mainInput = document.querySelector('.currency-input[is-main="true"]');
    console.warn(
      'Input principal:',
      mainInput ? '✅ Encontrado (não será alterado)' : '❌ Não encontrado'
    );
    if (mainInput) console.warn('  - Valor atual:', mainInput.value);

    // Verifica prompt input
    console.warn('Prompt input:', this.PromptInput ? '✅ Encontrado' : '❌ Não encontrado');

    // Verifica botão process
    console.warn(
      'Botão process:',
      this.ProcessPromptButton ? '✅ Encontrado' : '❌ Não encontrado'
    );

    // Verifica itens patrimoniais com input-settings="receive"
    const patrimonyItems = document.querySelectorAll('.patrimonio_interactive_item');
    const receiveInputs = document.querySelectorAll('input[input-settings="receive"]');
    console.warn(`Itens patrimoniais: ${patrimonyItems.length} encontrados`);
    console.warn(`Inputs individuais (receive): ${receiveInputs.length} encontrados`);

    // Verifica container wrapper
    const wrapper = document.querySelector('.patrimonio_interactive_content-wrapper');
    console.warn('Container wrapper:', wrapper ? '✅ Encontrado' : '❌ Não encontrado');

    // Verifica sistema ReinoCalculator
    console.warn(
      'ReinoCalculator:',
      window.ReinoCalculator ? '✅ Disponível' : '❌ Não disponível'
    );
    if (window.ReinoCalculator && window.ReinoCalculator.systems) {
      const patrimonySyncModule = window.ReinoCalculator.systems.patrimonySync;
      console.warn('PatrimonySync:', patrimonySyncModule ? '✅ Conectado' : '❌ Não conectado');
    }

    console.warn('=== FIM DA VERIFICAÇÃO ===');
  }

  // Função para debug de sincronização de valores
  debugSyncValues() {
    console.warn('=== DEBUG SINCRONIZAÇÃO DE VALORES ===');

    const mainValue = this.currentValue;
    console.warn(`💰 Patrimônio total: ${this.formatCurrency(mainValue)}`);

    const patrimonyItems = this.getPatrimonyItems();
    let totalAllocated = 0;

    patrimonyItems.forEach((item, index) => {
      if (item && item.input) {
        const inputValue = item.input.value;
        const parsedValue = this.parseValueCorrectly(inputValue);
        const percentage = mainValue > 0 ? (parsedValue / mainValue) * 100 : 0;

        totalAllocated += parsedValue;

        console.warn(`📊 Item ${index}:`, {
          produto: item.ativoProduct || 'N/A',
          valorInput: inputValue,
          valorParsed: parsedValue,
          valorFormatado: this.formatCurrency(parsedValue),
          porcentagem: percentage.toFixed(2) + '%',
        });
      }
    });

    const remaining = mainValue - totalAllocated;
    const remainingPercent = mainValue > 0 ? (remaining / mainValue) * 100 : 0;

    console.warn(
      `📈 Total alocado: ${this.formatCurrency(totalAllocated)} (${((totalAllocated / mainValue) * 100).toFixed(2)}%)`
    );
    console.warn(
      `💸 Patrimônio restante: ${this.formatCurrency(remaining)} (${remainingPercent.toFixed(2)}%)`
    );

    // Verifica elementos de patrimônio restante na UI
    const restanteEl = document.querySelector('.patrimonio-restante');
    const porcentagemRestanteEl = document.querySelector('.porcentagem-restante');

    console.warn('🖥️ Elementos UI:');
    console.warn(
      `  patrimonio-restante: ${restanteEl ? restanteEl.textContent : 'não encontrado'}`
    );
    console.warn(
      `  porcentagem-restante: ${porcentagemRestanteEl ? porcentagemRestanteEl.textContent : 'não encontrado'}`
    );

    console.warn('=== FIM DEBUG SINCRONIZAÇÃO ===');
  }

  // Expõe funções de debug globalmente para desenvolvimento
  exposeDebugFunctions() {
    if (typeof window !== 'undefined' && window.ReinoCalculator) {
      window.debugOpenAI = {
        mapping: () => this.debugPatrimonyMapping(),
        test: (term) => this.testCategoryMapping(term),
        items: () => this.getPatrimonyItems(),
        sync: () => this.debugSyncValues(),
        extract: (text) => this.extractAllocationsFromText(text),
        findMatch: (category) => this.findBestMatchingItem(category),
        testValue: (text) => this.testValueExtraction(text),
        testPercentage: (text) => this.testPercentageExtraction(text),
        elements: () => this.debugElementsCheck(),
        testRegex: (text) =>
          this.testRegexPatterns(
            text || '**ALOCAÇÃO SOLICITADA:**\n\n- CDB: R$ 700,00\n\n**RESUMO:**'
          ),
        reconnect: () => this.attemptConnection(),
        forceReconnect: () => {
          this.patrimonySync = null;
          this.getPatrimonySync();
        },
      };

      // Instruções de uso
      console.warn('  - debugOpenAI.mapping() para ver mapeamento atual');
      console.warn('  - debugOpenAI.testPercentage("30% em cdb") para testar percentuais');
      console.warn('  - debugOpenAI.testValue("tenho 100 mil") para testar valores');
      console.warn('  - debugOpenAI.testRegex() para testar padrões regex');
    }
  }

  // Função para testar padrões regex
  testRegexPatterns(text) {
    console.warn('=== TESTE DE PADRÕES REGEX ===');
    console.warn('Texto a testar:', text);

    const patterns = [
      // Padrão principal: "Categoria: X%" ou "Categoria X%"
      /(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)[\s\-:]*(\d+(?:[,.]\d+)?)\s*%/gi,
      // Padrão secundário: "X% para Categoria"
      /(\d+(?:[,.]\d+)?)\s*%\s*(?:para|em|de|do|da)?\s*(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)/gi,
      // Padrão bullet point: "• Categoria: X%"
      /[•\-*]\s*(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)[\s\-:]*(\d+(?:[,.]\d+)?)\s*%/gi,
      // Padrão para valores absolutos: "Categoria: R$ X" ou "- Categoria: R$ X"
      /[•\-*]?\s*(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)[\s\-:]*r\$\s*(\d+(?:[,.]\d+)*(?:[,.]\d{2})?)/gi,
      // Padrão para valores absolutos invertido: "R$ X em Categoria"
      /r\$\s*(\d+(?:[,.]\d+)*(?:[,.]\d{2})?)\s*(?:em|para|de|do|da)?\s*(renda\s+fixa|fundos?\s+de\s+investimento|renda\s+variável|outros?\s+investimentos?|cdb|lci|lca|tesouro|títulos?\s+públicos?|cri|cra|debêntures?|ações|fiis?|etfs?|estruturada?|criptomoedas?|commodities)/gi,
    ];

    patterns.forEach((pattern, index) => {
      console.warn(`\n🔍 Testando padrão ${index + 1}:`);
      pattern.lastIndex = 0; // Reset regex
      let match;
      let found = false;
      while ((match = pattern.exec(text)) !== null) {
        found = true;
        console.warn(`✅ Match encontrado:`, match);
        console.warn(`   Grupo 1: "${match[1]}"`);
        console.warn(`   Grupo 2: "${match[2]}"`);
      }
      if (!found) {
        console.warn(`❌ Nenhum match encontrado`);
      }
    });

    console.warn('=== FIM TESTE REGEX ===');
  }
}
