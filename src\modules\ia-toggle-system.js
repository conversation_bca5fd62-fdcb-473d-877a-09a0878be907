/**
 * IA Toggle System
 * Handles the interactive toggle functionality for the IA input wrapper
 * Uses ONLY Motion library exactly like the product system - NO CSS animations
 */
export class IAToggleSystem {
  constructor() {
    this.isInitialized = false;
    this.Motion = null;
    this.isIAVisible = false;
    this.isAnimating = false;

    // DOM elements
    this.iaButton = null;
    this.iaWrapper = null;
    this.graficosContainer = null;
    this.closeButton = null;
    this.parentContainer = null;
  }

  init() {
    if (this.isInitialized) {
      return;
    }

    document.addEventListener('DOMContentLoaded', () => {
      this.waitForMotion();
    });

    this.isInitialized = true;
  }

  waitForMotion() {
    if (window.Motion) {
      this.Motion = window.Motion;
      this.initIAToggleSystem();
    } else {
      setTimeout(() => this.waitForMotion(), 50);
    }
  }

  initIAToggleSystem() {
    const { animate, hover } = this.Motion;

    // Configuration matching the product system exactly
    const config = {
      duration: {
        fast: 0.3,
        normal: 0.5,
        slow: 0.6,
      },
      animation: {
        blur: 8,
        move: 15,
        scale: 0.95,
      },
      ease: 'circOut',
    };

    // Get DOM elements
    this.iaButton = document.querySelector('.ia-button-alocacao');
    this.iaWrapper = document.querySelector('.ia-input_wrapper');
    this.graficosContainer = document.querySelector('.graficos-distribuicao-ativos');
    this.closeButton = document.querySelector('.close-ia');
    this.parentContainer = document.querySelector('.componente-alocao-right');

    if (!this.iaButton || !this.iaWrapper || !this.graficosContainer) {
      console.warn('IA Toggle System: Required elements not found');
      return;
    }

    // Set initial state exactly like product system
    this.setupInitialState();

    // Setup event listeners
    this.setupEvents(animate, config);

    // Add hover effects using Motion
    this.setupHoverEffects(animate, hover);
  }

  setupInitialState() {
    // Set initial state like product system - IA hidden, graficos visible
    this.iaWrapper.style.display = 'none';
    this.graficosContainer.style.display = 'block';
    this.isIAVisible = false;
  }

  setupEvents(animate, config) {
    // Main toggle button click
    if (this.iaButton) {
      this.iaButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.toggleIA(animate, config);
      });
    }

    // Close button click
    if (this.closeButton) {
      this.closeButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (this.isIAVisible && !this.isAnimating) {
          this.hideIA(animate, config);
        }
      });
    }

    // Close on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isIAVisible && !this.isAnimating) {
        this.hideIA(animate, config);
      }
    });

    // Optional: Close when clicking outside
    document.addEventListener('click', (e) => {
      if (this.isIAVisible && !this.isAnimating) {
        const isClickInsideIA = this.iaWrapper.contains(e.target);
        const isClickOnButton = this.iaButton.contains(e.target);

        if (!isClickInsideIA && !isClickOnButton) {
          this.hideIA(animate, config);
        }
      }
    });
  }

  setupHoverEffects(animate, hover) {
    // IA Button hover effect using Motion like product system
    if (this.iaButton && hover) {
      hover(this.iaButton, (element) => {
        animate(
          element,
          {
            scale: 1.02,
            y: -2,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          },
          {
            duration: 0.2,
            ease: 'circOut',
          }
        );

        return () => {
          animate(
            element,
            {
              scale: 1,
              y: 0,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            },
            {
              duration: 0.15,
              ease: 'circOut',
            }
          );
        };
      });
    }

    // Close button hover effect
    if (this.closeButton && hover) {
      hover(this.closeButton, (element) => {
        animate(
          element,
          {
            scale: 1.1,
            rotate: 90,
          },
          {
            duration: 0.3,
            ease: 'backOut',
          }
        );

        return () => {
          animate(
            element,
            {
              scale: 1,
              rotate: 0,
            },
            {
              duration: 0.2,
              ease: 'circOut',
            }
          );
        };
      });
    }
  }

  async toggleIA(animate, config) {
    if (this.isAnimating) return;

    if (this.isIAVisible) {
      await this.hideIA(animate, config);
    } else {
      await this.showIA(animate, config);
    }
  }

  async showIA(animate, config) {
    if (this.isIAVisible || this.isAnimating) return;

    this.isAnimating = true;
    this.isIAVisible = true;

    // Step 1: Animate graficos container EXIT (like disabledDiv in product system)
    await animate(
      this.graficosContainer,
      {
        opacity: 0,
        y: -config.animation.move,
        filter: `blur(${config.animation.blur}px)`,
      },
      {
        duration: config.duration.fast,
        ease: 'circIn',
      }
    ).finished;

    // Step 2: Switch display (like product system)
    this.graficosContainer.style.display = 'none';
    this.iaWrapper.style.display = 'flex';

    // Step 3: Animate IA wrapper ENTRANCE (like activeDiv in product system)
    await animate(
      this.iaWrapper,
      {
        opacity: [0, 1],
        y: [config.animation.move, 0],
        filter: ['blur(5px)', 'blur(0px)'],
      },
      {
        duration: config.duration.normal,
        ease: 'backOut',
      }
    ).finished;

    this.isAnimating = false;

    // Focus on input for better UX
    const promptInput = this.iaWrapper.querySelector('.prompt-input');
    if (promptInput) {
      setTimeout(() => promptInput.focus(), 100);
    }

    // Dispatch custom event
    this.dispatchToggleEvent('ia-shown');
  }

  async hideIA(animate, config) {
    if (!this.isIAVisible || this.isAnimating) return;

    this.isAnimating = true;
    this.isIAVisible = false;

    // Step 1: Animate IA wrapper EXIT (like activeDiv in product system)
    await animate(
      this.iaWrapper,
      {
        opacity: 0,
        y: config.animation.move / 2,
        filter: 'blur(5px)',
      },
      {
        duration: config.duration.fast,
        ease: config.ease,
      }
    ).finished;

    // Step 2: Switch display (like product system)
    this.iaWrapper.style.display = 'none';
    this.graficosContainer.style.display = 'flex';

    // Step 3: Animate graficos container ENTRANCE (like disabledDiv in product system)
    await animate(
      this.graficosContainer,
      {
        opacity: [0, 1],
        y: [0, 0],
        filter: ['blur(5px)', 'blur(0px)'],
      },
      {
        duration: config.duration.normal,
        ease: config.ease,
      }
    ).finished;

    this.isAnimating = false;

    // Dispatch custom event
    this.dispatchToggleEvent('ia-hidden');
  }

  dispatchToggleEvent(eventType) {
    const event = new CustomEvent(eventType, {
      detail: {
        isVisible: this.isIAVisible,
        timestamp: Date.now(),
      },
    });
    document.dispatchEvent(event);
  }

  // Public methods for external control
  show() {
    if (this.Motion && !this.isIAVisible && !this.isAnimating) {
      const config = {
        duration: { fast: 0.3, normal: 0.5 },
        animation: { blur: 8, move: 15, scale: 0.95 },
        ease: 'circOut',
      };
      this.showIA(this.Motion.animate, config);
    }
  }

  hide() {
    if (this.Motion && this.isIAVisible && !this.isAnimating) {
      const config = {
        duration: { fast: 0.3, normal: 0.5 },
        animation: { blur: 8, move: 15, scale: 0.95 },
        ease: 'circOut',
      };
      this.hideIA(this.Motion.animate, config);
    }
  }

  toggle() {
    if (this.Motion && !this.isAnimating) {
      const config = {
        duration: { fast: 0.3, normal: 0.5 },
        animation: { blur: 8, move: 15, scale: 0.95 },
        ease: 'circOut',
      };
      this.toggleIA(this.Motion.animate, config);
    }
  }

  // Getters for current state
  get visible() {
    return this.isIAVisible;
  }

  get animating() {
    return this.isAnimating;
  }
}
