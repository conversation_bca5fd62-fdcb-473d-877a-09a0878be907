/**
 * Test Script for AtivosGraficoSync Integration
 * Verifies that the synchronization between patrimonio items and ativos-grafico items works correctly
 */

class AtivosGraficoTestSuite {
  constructor() {
    this.testResults = [];
    this.ativosSync = null;
    this.patrimonySync = null;
    this.isTestRunning = false;
  }

  async init() {
    console.log('🧪 Initializing AtivosGrafico Test Suite...');

    // Wait for systems to be available
    await this.waitForSystems();

    // Get module references
    this.ativosSync = window.AppCalcReino?.getModule('ativosGraficoSync');
    this.patrimonySync = window.AppCalcReino?.getModule('patrimonySync');

    if (!this.ativosSync || !this.patrimonySync) {
      console.error('❌ Required systems not available');
      return false;
    }

    console.log('✅ Test suite initialized');
    return true;
  }

  waitForSystems() {
    return new Promise((resolve) => {
      const check = () => {
        if (window.AppCalcReino &&
            window.AppCalcReino.getModule('ativosGraficoSync') &&
            window.AppCalcReino.getModule('patrimonySync')) {
          resolve();
        } else {
          setTimeout(check, 100);
        }
      };
      check();
    });
  }

  async runAllTests() {
    if (this.isTestRunning) {
      console.warn('⚠️ Tests already running');
      return;
    }

    this.isTestRunning = true;
    this.testResults = [];

    console.log('🚀 Starting AtivosGrafico integration tests...');

    try {
      // Test 1: System initialization
      await this.testSystemInitialization();

      // Test 2: Element mapping
      await this.testElementMapping();

      // Test 3: Basic synchronization
      await this.testBasicSynchronization();

      // Test 4: Animation integration
      await this.testAnimationIntegration();

      // Test 5: Data consistency
      await this.testDataConsistency();

      // Test 6: Edge cases
      await this.testEdgeCases();

      // Generate report
      this.generateTestReport();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
    } finally {
      this.isTestRunning = false;
    }
  }

  async testSystemInitialization() {
    console.log('📋 Test 1: System Initialization');

    const test = {
      name: 'System Initialization',
      passed: true,
      details: []
    };

    // Check if AtivosGraficoSync is initialized
    if (!this.ativosSync || !this.ativosSync.isInitialized) {
      test.passed = false;
      test.details.push('AtivosGraficoSync not initialized');
    } else {
      test.details.push('✅ AtivosGraficoSync initialized');
    }

    // Check if PatrimonySync is initialized
    if (!this.patrimonySync || !this.patrimonySync.isInitialized) {
      test.passed = false;
      test.details.push('PatrimonySync not initialized');
    } else {
      test.details.push('✅ PatrimonySync initialized');
    }

    // Check GSAP availability
    if (!window.gsap) {
      test.passed = false;
      test.details.push('GSAP not available');
    } else {
      test.details.push('✅ GSAP available');
    }

    this.testResults.push(test);
    this.logTestResult(test);
  }

  async testElementMapping() {
    console.log('📋 Test 2: Element Mapping');

    const test = {
      name: 'Element Mapping',
      passed: true,
      details: []
    };

    // Check if ativos-grafico elements are found
    const ativosElements = document.querySelectorAll('.ativos-grafico-item');
    const patrimonyElements = document.querySelectorAll('.patrimonio_interactive_item');

    if (ativosElements.length === 0) {
      test.passed = false;
      test.details.push('No ativos-grafico-item elements found');
    } else {
      test.details.push(`✅ Found ${ativosElements.length} ativos-grafico items`);
    }

    if (patrimonyElements.length === 0) {
      test.passed = false;
      test.details.push('No patrimonio_interactive_item elements found');
    } else {
      test.details.push(`✅ Found ${patrimonyElements.length} patrimony items`);
    }

    // Check attribute mapping
    let mappedCount = 0;
    ativosElements.forEach((element) => {
      const category = element.getAttribute('ativo-category');
      const product = element.getAttribute('ativo-product');

      if (category && product) {
        mappedCount++;

        // Check if corresponding patrimony item exists
        const matchingPatrimony = Array.from(patrimonyElements).find(p =>
          p.getAttribute('ativo-category') === category &&
          p.getAttribute('ativo-product') === product
        );

        if (matchingPatrimony) {
          test.details.push(`✅ Mapped: ${category} - ${product}`);
        } else {
          test.passed = false;
          test.details.push(`❌ No matching patrimony for: ${category} - ${product}`);
        }
      }
    });

    test.details.push(`Mapped ${mappedCount}/${ativosElements.length} items`);

    this.testResults.push(test);
    this.logTestResult(test);
  }

  async testBasicSynchronization() {
    console.log('📋 Test 3: Basic Synchronization');

    const test = {
      name: 'Basic Synchronization',
      passed: true,
      details: []
    };

    // Test manual allocation update
    try {
      // Set a test patrimony value
      const testValue = 100000;
      this.patrimonySync.setMainValue(testValue);
      test.details.push(`✅ Set main value: ${testValue}`);

      await this.sleep(500); // Wait for sync

      // Test allocation for a specific item
      const testCategory = 'Renda Fixa';
      const testProduct = 'CDB';
      const testAllocation = 25000; // 25% of 100k

      // Simulate allocation change
      this.ativosSync.updateAllocation(testCategory, testProduct, testAllocation, 25);
      test.details.push(`✅ Updated allocation: ${testCategory} - ${testProduct} = ${testAllocation}`);

      await this.sleep(1000); // Wait for animation

      // Check if visual elements updated
      const ativoItem = this.ativosSync.getItemByKey(testCategory, testProduct);
      if (ativoItem) {
        const barWidth = ativoItem.barraElement.style.width;
        const percentageText = ativoItem.porcentagemElement.textContent;

        test.details.push(`Bar width: ${barWidth}, Percentage text: ${percentageText}`);

        if (barWidth.includes('25') && percentageText.includes('25')) {
          test.details.push('✅ Visual elements updated correctly');
        } else {
          test.passed = false;
          test.details.push('❌ Visual elements not updated correctly');
        }
      } else {
        test.passed = false;
        test.details.push('❌ Ativo item not found');
      }

    } catch (error) {
      test.passed = false;
      test.details.push(`❌ Error in synchronization: ${error.message}`);
    }

    this.testResults.push(test);
    this.logTestResult(test);
  }

  async testAnimationIntegration() {
    console.log('📋 Test 4: Animation Integration');

    const test = {
      name: 'Animation Integration',
      passed: true,
      details: []
    };

    try {
      // Test entrance animation
      const timeline = this.ativosSync.playEntranceAnimation();
      if (timeline) {
        test.details.push('✅ Entrance animation triggered');
      } else {
        test.passed = false;
        test.details.push('❌ Entrance animation failed');
      }

      await this.sleep(1000);

      // Test sequential animation
      const sequentialTimeline = this.ativosSync.animateAllBarsSequentially();
      if (sequentialTimeline) {
        test.details.push('✅ Sequential animation triggered');
      } else {
        test.passed = false;
        test.details.push('❌ Sequential animation failed');
      }

      await this.sleep(2000);

      // Test reset
      this.ativosSync.resetAllBars();
      test.details.push('✅ Reset animation completed');

    } catch (error) {
      test.passed = false;
      test.details.push(`❌ Animation error: ${error.message}`);
    }

    this.testResults.push(test);
    this.logTestResult(test);
  }

  async testDataConsistency() {
    console.log('📋 Test 5: Data Consistency');

    const test = {
      name: 'Data Consistency',
      passed: true,
      details: []
    };

    try {
      // Test multiple allocations
      const allocations = [
        { category: 'Renda Fixa', product: 'CDB', value: 30000, percentage: 30 },
        { category: 'Renda Fixa', product: 'CRI', value: 20000, percentage: 20 },
        { category: 'Fundo de investimento', product: 'Ações', value: 25000, percentage: 25 },
        { category: 'Renda Variável', product: 'Ações', value: 15000, percentage: 15 },
      ];

      let totalPercentage = 0;

      for (const allocation of allocations) {
        this.ativosSync.updateAllocation(
          allocation.category,
          allocation.product,
          allocation.value,
          allocation.percentage
        );
        totalPercentage += allocation.percentage;
        test.details.push(`Updated: ${allocation.category} - ${allocation.product} = ${allocation.percentage}%`);
      }

      await this.sleep(1500);

      // Check if percentages add up correctly (allowing for rounding)
      if (Math.abs(totalPercentage - 90) < 1) { // 90% allocated, 10% remaining
        test.details.push('✅ Percentage calculations consistent');
      } else {
        test.passed = false;
        test.details.push(`❌ Percentage inconsistency: total = ${totalPercentage}%`);
      }

      // Verify visual consistency
      const allItems = this.ativosSync.getAllItems();
      let visualTotalPercentage = 0;

      allItems.forEach((item) => {
        const currentPercentage = parseFloat(item.porcentagemElement.textContent) || 0;
        visualTotalPercentage += currentPercentage;
      });

      test.details.push(`Visual total: ${visualTotalPercentage.toFixed(1)}%`);

    } catch (error) {
      test.passed = false;
      test.details.push(`❌ Data consistency error: ${error.message}`);
    }

    this.testResults.push(test);
    this.logTestResult(test);
  }

  async testEdgeCases() {
    console.log('📋 Test 6: Edge Cases');

    const test = {
      name: 'Edge Cases',
      passed: true,
      details: []
    };

    try {
      // Test zero allocation
      this.ativosSync.updateAllocation('Outros', 'Poupança', 0, 0);
      test.details.push('✅ Zero allocation handled');

      // Test 100% allocation
      this.ativosSync.updateAllocation('Renda Fixa', 'CDB', 100000, 100);
      test.details.push('✅ 100% allocation handled');

      await this.sleep(1000);

      // Test invalid category/product
      this.ativosSync.updateAllocation('Invalid Category', 'Invalid Product', 1000, 10);
      test.details.push('✅ Invalid category/product handled gracefully');

      // Test negative values (should be handled gracefully)
      this.ativosSync.updateAllocation('Renda Fixa', 'CDB', -1000, -10);
      test.details.push('✅ Negative values handled');

      // Test very large values
      this.ativosSync.updateAllocation('Renda Fixa', 'CDB', 999999999, 150);
      test.details.push('✅ Large values handled');

    } catch (error) {
      test.passed = false;
      test.details.push(`❌ Edge case error: ${error.message}`);
    }

    this.testResults.push(test);
    this.logTestResult(test);
  }

  generateTestReport() {
    console.log('\n📊 TEST REPORT');
    console.log('='.repeat(50));

    const passedTests = this.testResults.filter(t => t.passed).length;
    const totalTests = this.testResults.length;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);

    console.log(`Overall Result: ${passedTests}/${totalTests} tests passed (${successRate}%)`);
    console.log('');

    this.testResults.forEach((test, index) => {
      const status = test.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${index + 1}. ${test.name}: ${status}`);

      if (test.details.length > 0) {
        test.details.forEach(detail => {
          console.log(`   ${detail}`);
        });
      }
      console.log('');
    });

    // Debug information
    if (this.ativosSync) {
      console.log('🔍 Debug Information:');
      console.log(this.ativosSync.getDebugInfo());
    }

    console.log('='.repeat(50));
  }

  logTestResult(test) {
    const status = test.passed ? '✅' : '❌';
    console.log(`${status} ${test.name}: ${test.passed ? 'PASSED' : 'FAILED'}`);

    if (!test.passed) {
      test.details.forEach(detail => {
        if (detail.startsWith('❌')) {
          console.log(`  ${detail}`);
        }
      });
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Manual test methods for interactive testing

  async manualTestAllocation(category, product, percentage) {
    console.log(`🧪 Manual Test: ${category} - ${product} = ${percentage}%`);

    if (!this.ativosSync) {
      console.error('❌ AtivosSync not available');
      return;
    }

    const value = (this.patrimonySync?.getMainValue() || 100000) * (percentage / 100);
    this.ativosSync.updateAllocation(category, product, value, percentage);

    await this.sleep(1000);

    const item = this.ativosSync.getItemByKey(category, product);
    if (item) {
      console.log(`Result: Bar width = ${item.barraElement.style.width}, Text = ${item.porcentagemElement.textContent}`);
    } else {
      console.log('❌ Item not found');
    }
  }

  async manualTestReset() {
    console.log('🧪 Manual Test: Reset All');

    if (this.ativosSync) {
      this.ativosSync.resetAllBars();
      console.log('✅ Reset completed');
    } else {
      console.error('❌ AtivosSync not available');
    }
  }

  async manualTestSequentialAnimation() {
    console.log('🧪 Manual Test: Sequential Animation');

    if (this.ativosSync) {
      this.ativosSync.animateAllBarsSequentially();
      console.log('✅ Animation started');
    } else {
      console.error('❌ AtivosSync not available');
    }
  }
}

// Auto-initialize and expose globally
const testSuite = new AtivosGraficoTestSuite();

// Wait for DOM and systems to be ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', async () => {
    await testSuite.init();
  });
} else {
  testSuite.init();
}

// Expose globally for manual testing
window.AtivosGraficoTestSuite = testSuite;

// Export for module usage
export { AtivosGraficoTestSuite };

// Console helpers for manual testing
window.testAtivos = {
  run: () => testSuite.runAllTests(),
  manual: {
    allocation: (category, product, percentage) => testSuite.manualTestAllocation(category, product, percentage),
    reset: () => testSuite.manualTestReset(),
    animate: () => testSuite.manualTestSequentialAnimation(),
  },
  debug: () => {
    if (testSuite.ativosSync) {
      console.log(testSuite.ativosSync.getDebugInfo());
    } else {
      console.log('AtivosSync not available');
    }
  }
};

console.log('🧪 AtivosGrafico Test Suite loaded. Use window.testAtivos.run() to start tests.');
