/**
 * IA Toggle Test Module
 * Test file to demonstrate and validate IA Toggle functionality
 * Shows how the system works exactly like product-system.js
 */

export class IAToggleTest {
  constructor() {
    this.iaToggleSystem = null;
    this.testResults = [];
  }

  init() {
    document.addEventListener('DOMContentLoaded', () => {
      this.waitForSystems();
    });
  }

  waitForSystems() {
    // Wait for ReinoCalculator and IA Toggle System to be available
    if (window.ReinoCalculator && window.ReinoCalculator.systems.iaToggle) {
      this.iaToggleSystem = window.ReinoCalculator.systems.iaToggle;
      this.setupTests();
    } else {
      setTimeout(() => this.waitForSystems(), 100);
    }
  }

  setupTests() {
    console.log('🧪 IA Toggle Test System initialized');

    // Add test buttons to the page
    this.addTestControls();

    // Listen for IA toggle events
    this.setupEventListeners();

    // Run initial tests
    this.runBasicTests();
  }

  addTestControls() {
    // Create test panel
    const testPanel = document.createElement('div');
    testPanel.id = 'ia-toggle-test-panel';
    testPanel.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      border: 2px solid #667eea;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      z-index: 9999;
      font-family: monospace;
      font-size: 12px;
      max-width: 300px;
    `;

    testPanel.innerHTML = `
      <h4 style="margin: 0 0 10px 0; color: #667eea;">🧪 IA Toggle Tests</h4>

      <div style="margin-bottom: 10px;">
        <strong>Manual Controls:</strong>
      </div>

      <button id="test-show-ia" style="margin: 2px; padding: 5px 8px; font-size: 11px;">Show IA</button>
      <button id="test-hide-ia" style="margin: 2px; padding: 5px 8px; font-size: 11px;">Hide IA</button>
      <button id="test-toggle-ia" style="margin: 2px; padding: 5px 8px; font-size: 11px;">Toggle IA</button>

      <div style="margin: 10px 0;">
        <strong>Auto Tests:</strong>
      </div>

      <button id="test-animation-sequence" style="margin: 2px; padding: 5px 8px; font-size: 11px;">Animation Test</button>
      <button id="test-rapid-clicks" style="margin: 2px; padding: 5px 8px; font-size: 11px;">Rapid Click Test</button>

      <div id="test-status" style="margin-top: 10px; padding: 8px; background: #f5f5f5; border-radius: 4px; font-size: 10px;">
        Status: Ready
      </div>

      <div id="test-results" style="margin-top: 8px; max-height: 200px; overflow-y: auto; font-size: 10px;">
      </div>
    `;

    document.body.appendChild(testPanel);

    // Add event listeners to test buttons
    this.setupTestButtons();
  }

  setupTestButtons() {
    document.getElementById('test-show-ia').addEventListener('click', () => {
      this.logTest('Manual Show IA');
      this.iaToggleSystem.show();
    });

    document.getElementById('test-hide-ia').addEventListener('click', () => {
      this.logTest('Manual Hide IA');
      this.iaToggleSystem.hide();
    });

    document.getElementById('test-toggle-ia').addEventListener('click', () => {
      this.logTest('Manual Toggle IA');
      this.iaToggleSystem.toggle();
    });

    document.getElementById('test-animation-sequence').addEventListener('click', () => {
      this.runAnimationSequenceTest();
    });

    document.getElementById('test-rapid-clicks').addEventListener('click', () => {
      this.runRapidClickTest();
    });
  }

  setupEventListeners() {
    // Listen for IA toggle events
    document.addEventListener('ia-shown', (event) => {
      this.logTest('✅ IA Shown Event', event.detail);
    });

    document.addEventListener('ia-hidden', (event) => {
      this.logTest('✅ IA Hidden Event', event.detail);
    });

    // Monitor state changes
    this.monitorStateChanges();
  }

  monitorStateChanges() {
    let lastState = this.iaToggleSystem.visible;

    setInterval(() => {
      const currentState = this.iaToggleSystem.visible;
      if (currentState !== lastState) {
        this.logTest(`State Change: ${lastState ? 'visible' : 'hidden'} → ${currentState ? 'visible' : 'hidden'}`);
        lastState = currentState;
      }
    }, 100);
  }

  runBasicTests() {
    this.logTest('🚀 Running Basic Tests');

    // Test 1: Check initial state
    const initialState = this.iaToggleSystem.visible;
    this.logTest(`Initial State: ${initialState ? 'visible' : 'hidden'}`);

    // Test 2: Check if elements exist
    const iaWrapper = document.querySelector('.ia-input_wrapper');
    const graficosContainer = document.querySelector('.graficos-distribuicao-ativos');
    const iaButton = document.querySelector('.ia-button-alocacao');

    this.logTest(`Elements Check:`);
    this.logTest(`  IA Wrapper: ${iaWrapper ? '✅' : '❌'}`);
    this.logTest(`  Graficos Container: ${graficosContainer ? '✅' : '❌'}`);
    this.logTest(`  IA Button: ${iaButton ? '✅' : '❌'}`);

    // Test 3: Check display states
    if (iaWrapper && graficosContainer) {
      const iaDisplay = window.getComputedStyle(iaWrapper).display;
      const graficosDisplay = window.getComputedStyle(graficosContainer).display;

      this.logTest(`Display States:`);
      this.logTest(`  IA Wrapper: ${iaDisplay}`);
      this.logTest(`  Graficos: ${graficosDisplay}`);
    }
  }

  async runAnimationSequenceTest() {
    this.logTest('🎬 Running Animation Sequence Test');
    this.updateStatus('Running animation test...');

    try {
      // Test sequence: hide → show → hide → show
      const sequence = [
        { action: 'hide', delay: 500 },
        { action: 'show', delay: 1000 },
        { action: 'hide', delay: 1000 },
        { action: 'show', delay: 1000 },
        { action: 'hide', delay: 500 }
      ];

      for (const step of sequence) {
        await this.delay(step.delay);

        if (step.action === 'show') {
          this.iaToggleSystem.show();
          this.logTest(`📥 Show command sent`);
        } else {
          this.iaToggleSystem.hide();
          this.logTest(`📤 Hide command sent`);
        }
      }

      this.logTest('✅ Animation sequence test completed');
      this.updateStatus('Animation test completed');

    } catch (error) {
      this.logTest(`❌ Animation test failed: ${error.message}`);
      this.updateStatus('Animation test failed');
    }
  }

  async runRapidClickTest() {
    this.logTest('⚡ Running Rapid Click Test');
    this.updateStatus('Running rapid click test...');

    try {
      // Simulate rapid clicks
      for (let i = 0; i < 10; i++) {
        this.iaToggleSystem.toggle();
        this.logTest(`Click ${i + 1} - State: ${this.iaToggleSystem.visible ? 'visible' : 'hidden'}, Animating: ${this.iaToggleSystem.animating ? 'yes' : 'no'}`);
        await this.delay(100); // 100ms between clicks
      }

      this.logTest('✅ Rapid click test completed');
      this.updateStatus('Rapid click test completed');

    } catch (error) {
      this.logTest(`❌ Rapid click test failed: ${error.message}`);
      this.updateStatus('Rapid click test failed');
    }
  }

  logTest(message, data = null) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;

    console.log(logEntry, data || '');

    // Add to UI
    const resultsDiv = document.getElementById('test-results');
    if (resultsDiv) {
      const logDiv = document.createElement('div');
      logDiv.style.cssText = 'margin: 2px 0; padding: 2px; border-left: 2px solid #667eea; padding-left: 5px;';
      logDiv.textContent = logEntry;
      resultsDiv.appendChild(logDiv);
      resultsDiv.scrollTop = resultsDiv.scrollHeight;
    }

    this.testResults.push({ timestamp, message, data });
  }

  updateStatus(status) {
    const statusDiv = document.getElementById('test-status');
    if (statusDiv) {
      statusDiv.textContent = `Status: ${status}`;
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Public API for console debugging
  getTestResults() {
    return this.testResults;
  }

  clearResults() {
    this.testResults = [];
    const resultsDiv = document.getElementById('test-results');
    if (resultsDiv) {
      resultsDiv.innerHTML = '';
    }
    this.logTest('🧹 Test results cleared');
  }

  showSystemInfo() {
    this.logTest('📊 System Information:');
    this.logTest(`  IA Visible: ${this.iaToggleSystem.visible}`);
    this.logTest(`  Is Animating: ${this.iaToggleSystem.animating}`);
    this.logTest(`  Motion Library: ${window.Motion ? '✅' : '❌'}`);
    this.logTest(`  Reino Calculator: ${window.ReinoCalculator ? '✅' : '❌'}`);
  }
}

// Create and initialize test system
const iaToggleTest = new IAToggleTest();
iaToggleTest.init();

// Add to global scope for console access
window.IAToggleTest = iaToggleTest;

// Console helper message
console.log(`
🧪 IA Toggle Test System loaded!

Available console commands:
  • IAToggleTest.showSystemInfo() - Show current system state
  • IAToggleTest.getTestResults() - Get all test results
  • IAToggleTest.clearResults() - Clear test results
  • IAToggleTest.logTest('message') - Add custom log entry

Access the IA Toggle System directly:
  • ReinoCalculator.ui.iaToggle.show()
  • ReinoCalculator.ui.iaToggle.hide()
  • ReinoCalculator.ui.iaToggle.toggle()
  • ReinoCalculator.ui.iaToggle.visible
  • ReinoCalculator.ui.iaToggle.animating
`);
