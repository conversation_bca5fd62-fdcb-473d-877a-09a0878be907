/**
 * Chart Animation System
 * Handles GSAP-based chart animations synchronized with range sliders
 * Updated to work with both legacy chart system and new ativos-grafico-item structure
 */
class ChartAnimationSystem {
  constructor() {
    this.isInitialized = false;
    this.timeline = null;
    this.ativosTimeline = null;
    this.ativosItems = new Map();
  }

  init() {
    if (this.isInitialized) {
      return;
    }

    document.addEventListener('DOMContentLoaded', () => {
      this.waitForGSAP();
    });

    this.isInitialized = true;
  }

  waitForGSAP() {
    if (window.gsap) {
      this.initializeChartSystem();
    } else {
      setTimeout(() => this.waitForGSAP(), 50);
    }
  }

  initializeChartSystem() {
    // Initialize legacy chart system
    if (window.$) {
      this.initializeWithJQuery();
    } else {
      this.initializeVanilla();
    }

    // Initialize new ativos-grafico system
    this.initializeAtivosGraficoSystem();
  }

  initializeWithJQuery() {
    const { $ } = window;

    // Inicializa o gráfico único
    $('.chart_wrap').each((index, element) => {
      this.timeline = window.gsap.timeline({
        paused: true,
      });

      $(element)
        .find('.chart_column')
        .each((columnIndex, columnElement) => {
          let bar = $(columnElement).find('.chart_bar');
          let start = +$(columnElement).attr('start');
          let end = +$(columnElement).attr('end');
          let numberText = $(columnElement).find('.chart_number');
          let number = { value: start };

          this.timeline
            .fromTo(
              bar,
              {
                height: start + '%',
              },
              {
                height: end + '%',
              },
              '<'
            )
            .fromTo(
              number,
              {
                value: start,
              },
              {
                value: end,
                onUpdate: () => numberText.text(Math.round(number.value)),
              },
              '<'
            );
        });
    });

    // Conecta TODOS os sliders ao mesmo gráfico
    $('range-slider.slider').each((index, element) => {
      // CORRIGE O STEP PROBLEMÁTICO
      element.setAttribute('step', '0.01'); // Muda de 0.00000001 para 0.01

      element.addEventListener('input', () => {
        if (this.timeline) {
          this.timeline.progress(element.value);
        }
      });
    });
  }

  initializeAtivosGraficoSystem() {
    // Find all ativos-grafico-item elements
    const ativosElements = document.querySelectorAll('.ativos-grafico-item');

    if (ativosElements.length === 0) {
      console.log('No ativos-grafico-item elements found');
      return;
    }

    // Create timeline for ativos animations
    this.ativosTimeline = window.gsap.timeline({ paused: true });

    ativosElements.forEach((element, index) => {
      const category = element.getAttribute('ativo-category');
      const product = element.getAttribute('ativo-product');
      const barraElement = element.querySelector('.barra-porcentagem-item');
      const porcentagemElement = element.querySelector('.porcentagem-float-alocacao');

      if (!barraElement || !porcentagemElement) {
        console.warn('Missing required elements in ativos-grafico-item:', element);
        return;
      }

      // Get target percentage from data attribute or calculate from width
      const targetPercentage = parseFloat(element.getAttribute('data-target-percentage')) || 0;

      const item = {
        element,
        category,
        product,
        barraElement,
        porcentagemElement,
        targetPercentage,
        index,
      };

      // Store item
      const key = `${category}_${product}`.replace(/\s+/g, '_');
      this.ativosItems.set(key, item);

      // Add to timeline
      this.addAtivoToTimeline(item, index);
    });

    // Connect ativos timeline to range sliders
    this.connectAtivosToSliders();

    console.log(`Initialized ${this.ativosItems.size} ativos-grafico items`);
  }

  addAtivoToTimeline(item, index) {
    const { barraElement, porcentagemElement, targetPercentage } = item;

    // Set initial state
    window.gsap.set(barraElement, { width: '0%' });

    // Create number animation object
    const numberObj = { value: 0 };

    // Add bar animation to timeline
    this.ativosTimeline.fromTo(
      barraElement,
      { width: '0%' },
      {
        width: `${targetPercentage}%`,
        duration: 0.8,
        ease: 'power2.out',
      },
      index * 0.1
    );

    // Add number animation to timeline
    this.ativosTimeline.fromTo(
      numberObj,
      { value: 0 },
      {
        value: targetPercentage,
        duration: 0.8,
        ease: 'power2.out',
        onUpdate: () => {
          const currentValue = Math.round(numberObj.value * 10) / 10;
          porcentagemElement.textContent = `${currentValue}%`;
        },
      },
      index * 0.1
    );
  }

  connectAtivosToSliders() {
    // Connect range sliders to ativos timeline
    const sliders = document.querySelectorAll('range-slider.slider');

    sliders.forEach((slider) => {
      // Fix step value for smoother animation
      slider.setAttribute('step', '0.01');

      // Add event listener for ativos timeline
      slider.addEventListener('input', () => {
        if (this.ativosTimeline) {
          this.ativosTimeline.progress(slider.value);
        }
      });
    });
  }

  updateAtivoPercentage(category, product, percentage) {
    const key = `${category}_${product}`.replace(/\s+/g, '_');
    const item = this.ativosItems.get(key);

    if (!item) {
      console.warn(`Ativo item not found: ${key}`);
      return;
    }

    // Update target percentage
    item.targetPercentage = percentage;

    // Animate to new percentage
    window.gsap.to(item.barraElement, {
      width: `${percentage}%`,
      duration: 0.6,
      ease: 'power2.out',
    });

    // Animate number
    const numberObj = { value: parseFloat(item.porcentagemElement.textContent) || 0 };
    window.gsap.to(numberObj, {
      value: percentage,
      duration: 0.6,
      ease: 'power2.out',
      onUpdate: () => {
        const currentValue = Math.round(numberObj.value * 10) / 10;
        item.porcentagemElement.textContent = `${currentValue}%`;
      },
    });
  }

  initializeVanilla() {
    // Vanilla JavaScript implementation
    const chartWraps = document.querySelectorAll('.chart_wrap');

    chartWraps.forEach((chartWrap) => {
      this.timeline = window.gsap.timeline({
        paused: true,
      });

      const chartColumns = chartWrap.querySelectorAll('.chart_column');

      chartColumns.forEach((column) => {
        const bar = column.querySelector('.chart_bar');
        const start = parseInt(column.getAttribute('start')) || 0;
        const end = parseInt(column.getAttribute('end')) || 0;
        const numberText = column.querySelector('.chart_number');
        const number = { value: start };

        if (bar && numberText) {
          this.timeline
            .fromTo(
              bar,
              {
                height: start + '%',
              },
              {
                height: end + '%',
              },
              '<'
            )
            .fromTo(
              number,
              {
                value: start,
              },
              {
                value: end,
                onUpdate: () => {
                  numberText.textContent = Math.round(number.value);
                },
              },
              '<'
            );
        }
      });
    });

    // Conecta TODOS os sliders ao mesmo gráfico
    const sliders = document.querySelectorAll('range-slider.slider');

    sliders.forEach((slider) => {
      // CORRIGE O STEP PROBLEMÁTICO
      slider.setAttribute('step', '0.01'); // Muda de 0.00000001 para 0.01

      slider.addEventListener('input', () => {
        if (this.timeline) {
          this.timeline.progress(slider.value);
        }
      });
    });
  }

  // Public API methods
  playChart() {
    if (this.timeline) {
      this.timeline.play();
    }
  }

  playAtivosChart() {
    if (this.ativosTimeline) {
      this.ativosTimeline.play();
    }
  }

  playAllCharts() {
    this.playChart();
    this.playAtivosChart();
  }

  pauseChart() {
    if (this.timeline) {
      this.timeline.pause();
    }
  }

  resetChart() {
    if (this.timeline) {
      this.timeline.progress(0);
    }
  }

  resetAtivosChart() {
    if (this.ativosTimeline) {
      this.ativosTimeline.progress(0);
    }

    // Reset percentage text
    this.ativosItems.forEach((item) => {
      item.porcentagemElement.textContent = '0%';
    });
  }

  resetAllCharts() {
    this.resetChart();
    this.resetAtivosChart();
  }

  setProgress(progress) {
    if (this.timeline && progress >= 0 && progress <= 1) {
      this.timeline.progress(progress);
    }
  }

  getProgress() {
    return this.timeline ? this.timeline.progress() : 0;
  }

  isPlaying() {
    return this.timeline ? this.timeline.isActive() : false;
  }

  getDuration() {
    return this.timeline ? this.timeline.duration() : 0;
  }

  // Advanced animation controls
  seekTo(time) {
    if (this.timeline && time >= 0) {
      this.timeline.seek(time);
    }
  }

  setTimeScale(scale) {
    if (this.timeline && scale > 0) {
      this.timeline.timeScale(scale);
    }
  }

  reverse() {
    if (this.timeline) {
      this.timeline.reverse();
    }
  }

  restart() {
    if (this.timeline) {
      this.timeline.restart();
    }
  }

  // Event handlers for external control
  onComplete(callback) {
    if (this.timeline && typeof callback === 'function') {
      this.timeline.eventCallback('onComplete', callback);
    }
  }

  onUpdate(callback) {
    if (this.timeline && typeof callback === 'function') {
      this.timeline.eventCallback('onUpdate', callback);
    }
  }

  onStart(callback) {
    if (this.timeline && typeof callback === 'function') {
      this.timeline.eventCallback('onStart', callback);
    }
  }

  // Cleanup
  destroy() {
    if (this.timeline) {
      this.timeline.kill();
      this.timeline = null;
    }

    if (this.ativosTimeline) {
      this.ativosTimeline.kill();
      this.ativosTimeline = null;
    }

    this.ativosItems.clear();
    this.isInitialized = false;
  }

  // New methods for ativos system integration

  getAtivoItem(category, product) {
    const key = `${category}_${product}`.replace(/\s+/g, '_');
    return this.ativosItems.get(key);
  }

  getAllAtivosItems() {
    return Array.from(this.ativosItems.values());
  }

  setAtivosProgress(progress) {
    if (this.ativosTimeline && progress >= 0 && progress <= 1) {
      this.ativosTimeline.progress(progress);
    }
  }

  getAtivosProgress() {
    return this.ativosTimeline ? this.ativosTimeline.progress() : 0;
  }
}

// Export for use in other modules
export { ChartAnimationSystem };
