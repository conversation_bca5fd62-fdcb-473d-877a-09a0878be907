/**
 * Ativos Grafico Sync System
 * Synchronizes chart visualization elements (ativos-grafico-item) with patrimony data
 * and handles GSAP animations for percentage bars and floating allocations
 */

export class AtivosGraficoSyncSystem {
  constructor() {
    this.isInitialized = false;
    this.gsap = null;
    this.ativosItems = new Map();
    this.patrimonySync = null;
    this.animations = new Map();
    this.totalPatrimony = 0;
    this.allocations = new Map();
  }

  async init() {
    if (this.isInitialized) {
      return;
    }

    // Wait for GSAP to be available
    await this.waitForGSAP();

    // Wait for PatrimonySyncSystem to be available
    await this.waitForPatrimonySync();

    // Initialize the system
    this.initializeAtivosItems();
    this.setupPatrimonyListeners();
    this.initializeAnimations();

    this.isInitialized = true;
    console.log('✅ AtivosGraficoSyncSystem initialized');
  }

  waitForGSAP() {
    return new Promise((resolve) => {
      const check = () => {
        if (window.gsap) {
          this.gsap = window.gsap;
          resolve();
        } else {
          setTimeout(check, 50);
        }
      };
      check();
    });
  }

  waitForPatrimonySync() {
    return new Promise((resolve) => {
      const check = () => {
        if (window.AppCalcReino && window.AppCalcReino.getModule) {
          this.patrimonySync = window.AppCalcReino.getModule('patrimonySync');
          if (this.patrimonySync) {
            resolve();
          } else {
            setTimeout(check, 50);
          }
        } else {
          setTimeout(check, 50);
        }
      };
      check();
    });
  }

  initializeAtivosItems() {
    // Find all ativos-grafico-item elements
    const ativosElements = document.querySelectorAll('.ativos-grafico-item');

    ativosElements.forEach((element) => {
      const category = element.getAttribute('ativo-category');
      const product = element.getAttribute('ativo-product');

      if (!category || !product) {
        console.warn('AtivosGraficoItem missing attributes:', element);
        return;
      }

      // Find child elements
      const barraElement = element.querySelector('.barra-porcentagem-item');
      const porcentagemElement = element.querySelector('.porcentagem-float-alocacao');
      const nomeElement = element.querySelector('.nome-produto-float');

      if (!barraElement || !porcentagemElement) {
        console.warn('AtivosGraficoItem missing required child elements:', element);
        return;
      }

      // Create item object
      const item = {
        element,
        category,
        product,
        barraElement,
        porcentagemElement,
        nomeElement,
        currentPercentage: 0,
        targetPercentage: 0,
        currentBarWidth: 0,
        targetBarWidth: 0,
      };

      // Create unique key for mapping
      const key = this.createItemKey(category, product);
      this.ativosItems.set(key, item);

      // Initialize visual state
      this.initializeItemVisualState(item);
    });

    console.log(`Found ${this.ativosItems.size} ativos-grafico items`);
  }

  createItemKey(category, product) {
    // Normalize strings for consistent matching
    const normalizeString = (str) => str.toLowerCase().trim().replace(/\s+/g, ' ');
    return `${normalizeString(category)}|${normalizeString(product)}`;
  }

  initializeItemVisualState(item) {
    // Set initial state with GSAP
    this.gsap.set(item.barraElement, {
      width: '0%',
      transformOrigin: 'left center',
    });

    this.gsap.set(item.porcentagemElement, {
      opacity: 1,
    });

    // Set initial percentage text
    item.porcentagemElement.textContent = '0%';
  }

  setupPatrimonyListeners() {
    if (!this.patrimonySync) {
      console.warn('PatrimonySync not available, retrying...');
      setTimeout(() => this.setupPatrimonyListeners(), 1000);
      return;
    }

    // Listen for allocation changes from PatrimonySync
    document.addEventListener('allocationChanged', (event) => {
      this.handleAllocationChange(event.detail);
    });

    // Listen for total patrimony changes
    document.addEventListener('totalPatrimonyChanged', (event) => {
      this.handleTotalPatrimonyChange(event.detail);
    });

    // Get initial allocations
    this.syncInitialAllocations();
  }

  syncInitialAllocations() {
    if (!this.patrimonySync) return;

    try {
      // Get current allocations from PatrimonySync
      const allocations = this.patrimonySync.getAllocations();
      const totalValue = this.patrimonySync.getMainValue();

      this.totalPatrimony = totalValue;

      // Update each ativos item based on allocations
      allocations.forEach((allocation) => {
        this.updateAtivoFromAllocation(allocation);
      });
    } catch (error) {
      console.warn('Could not sync initial allocations:', error);
    }
  }

  handleAllocationChange(allocationData) {
    const { category, product, value, percentage } = allocationData;

    if (!category || !product) return;

    // Update allocation map
    const key = this.createItemKey(category, product);
    this.allocations.set(key, {
      category,
      product,
      value,
      percentage,
    });

    // Update corresponding ativos item
    this.updateAtivoFromAllocation(allocationData);
  }

  handleTotalPatrimonyChange(totalData) {
    this.totalPatrimony = totalData.value || 0;

    // Recalculate percentages for all items
    this.recalculateAllPercentages();
  }

  updateAtivoFromAllocation(allocation) {
    const { category, product, value, percentage } = allocation;
    const key = this.createItemKey(category, product);
    const item = this.ativosItems.get(key);

    if (!item) {
      // Try alternative matching strategies
      const alternativeItem = this.findItemByAlternativeMatching(category, product);
      if (alternativeItem) {
        this.updateItemVisuals(alternativeItem, percentage || 0);
      }
      return;
    }

    // Calculate percentage if not provided
    let calculatedPercentage = percentage;
    if (!calculatedPercentage && this.totalPatrimony > 0) {
      calculatedPercentage = (value / this.totalPatrimony) * 100;
    }

    this.updateItemVisuals(item, calculatedPercentage || 0);
  }

  findItemByAlternativeMatching(category, product) {
    // Try different matching strategies for edge cases

    // Strategy 1: Partial product name matching
    for (const [key, item] of this.ativosItems) {
      if (this.fuzzyMatch(item.category, category) && this.fuzzyMatch(item.product, product)) {
        return item;
      }
    }

    // Strategy 2: Category-only matching for first available item
    for (const [key, item] of this.ativosItems) {
      if (this.fuzzyMatch(item.category, category)) {
        return item;
      }
    }

    return null;
  }

  fuzzyMatch(str1, str2) {
    const normalize = (str) => str.toLowerCase().trim().replace(/[^\w]/g, '');
    const norm1 = normalize(str1);
    const norm2 = normalize(str2);

    // Exact match
    if (norm1 === norm2) return true;

    // Contains match
    if (norm1.includes(norm2) || norm2.includes(norm1)) return true;

    return false;
  }

  updateItemVisuals(item, percentage) {
    const targetPercentage = Math.max(0, Math.min(100, percentage));

    // Don't animate if the change is very small
    if (Math.abs(item.currentPercentage - targetPercentage) < 0.1) {
      return;
    }

    item.targetPercentage = targetPercentage;

    // Kill existing animation for this item
    const existingAnimation = this.animations.get(item);
    if (existingAnimation) {
      existingAnimation.kill();
    }

    // Create animation timeline
    const timeline = this.gsap.timeline();

    // Animate percentage bar width
    timeline.to(item.barraElement, {
      width: `${targetPercentage}%`,
      duration: 0.8,
      ease: 'power2.out',
    });

    // Animate percentage text with counter effect
    const numberObj = { value: item.currentPercentage };
    timeline.to(numberObj, {
      value: targetPercentage,
      duration: 0.8,
      ease: 'power2.out',
      onUpdate: () => {
        const currentValue = Math.round(numberObj.value * 10) / 10;
        item.porcentagemElement.textContent = `${currentValue}%`;
      },
      onComplete: () => {
        // Ensure final value is exact
        item.porcentagemElement.textContent = `${Math.round(targetPercentage * 10) / 10}%`;
        item.currentPercentage = targetPercentage;
      }
    }, '<'); // Start at the same time as bar animation

    // Store animation for potential cancellation
    this.animations.set(item, timeline);

    // Add subtle pulse effect for significant changes
    if (Math.abs(item.currentPercentage - targetPercentage) > 5) {
      timeline.to(item.element, {
        scale: 1.02,
        duration: 0.2,
        ease: 'power2.out',
        yoyo: true,
        repeat: 1,
      }, '<');
    }
  }

  recalculateAllPercentages() {
    if (this.totalPatrimony <= 0) {
      // Reset all to 0% if no total
      this.ativosItems.forEach((item) => {
        this.updateItemVisuals(item, 0);
      });
      return;
    }

    // Recalculate each item based on current allocations
    this.allocations.forEach((allocation, key) => {
      const percentage = (allocation.value / this.totalPatrimony) * 100;
      this.updateAtivoFromAllocation({
        ...allocation,
        percentage,
      });
    });
  }

  // Animation control methods

  animateAllBarsSequentially() {
    const items = Array.from(this.ativosItems.values());
    const timeline = this.gsap.timeline();

    items.forEach((item, index) => {
      timeline.fromTo(item.barraElement, {
        width: '0%',
        opacity: 0,
      }, {
        width: `${item.targetPercentage}%`,
        opacity: 1,
        duration: 0.6,
        ease: 'power2.out',
      }, index * 0.1);

      // Animate percentage text
      const numberObj = { value: 0 };
      timeline.to(numberObj, {
        value: item.targetPercentage,
        duration: 0.6,
        ease: 'power2.out',
        onUpdate: () => {
          const currentValue = Math.round(numberObj.value * 10) / 10;
          item.porcentagemElement.textContent = `${currentValue}%`;
        },
      }, '<');
    });

    return timeline;
  }

  resetAllBars() {
    this.ativosItems.forEach((item) => {
      this.gsap.set(item.barraElement, { width: '0%' });
      item.porcentagemElement.textContent = '0%';
      item.currentPercentage = 0;
    });
  }

  playEntranceAnimation() {
    const timeline = this.gsap.timeline();

    this.ativosItems.forEach((item, index) => {
      timeline.fromTo(item.element, {
        opacity: 0,
        y: 20,
      }, {
        opacity: 1,
        y: 0,
        duration: 0.5,
        ease: 'power2.out',
      }, index * 0.05);
    });

    return timeline;
  }

  // Public API methods

  updateAllocation(category, product, value, percentage) {
    this.handleAllocationChange({
      category,
      product,
      value,
      percentage,
    });
  }

  getItemByKey(category, product) {
    const key = this.createItemKey(category, product);
    return this.ativosItems.get(key);
  }

  getAllItems() {
    return Array.from(this.ativosItems.values());
  }

  getItemsByCategory(category) {
    return Array.from(this.ativosItems.values()).filter(
      item => this.fuzzyMatch(item.category, category)
    );
  }

  // Event handling for external integration

  onWindowResize() {
    // Recalculate any responsive elements if needed
    this.ativosItems.forEach((item) => {
      // Trigger a visual refresh if needed
      if (item.currentPercentage > 0) {
        this.gsap.set(item.barraElement, { width: `${item.currentPercentage}%` });
      }
    });
  }

  // Cleanup methods

  destroy() {
    // Kill all animations
    this.animations.forEach((animation) => {
      animation.kill();
    });
    this.animations.clear();

    // Clear data
    this.ativosItems.clear();
    this.allocations.clear();

    // Remove event listeners
    document.removeEventListener('allocationChanged', this.handleAllocationChange);
    document.removeEventListener('totalPatrimonyChanged', this.handleTotalPatrimonyChange);

    this.isInitialized = false;
    console.log('🔄 AtivosGraficoSyncSystem destroyed');
  }

  // Debug methods

  getDebugInfo() {
    return {
      initialized: this.isInitialized,
      itemsCount: this.ativosItems.size,
      allocationsCount: this.allocations.size,
      totalPatrimony: this.totalPatrimony,
      items: Array.from(this.ativosItems.entries()).map(([key, item]) => ({
        key,
        category: item.category,
        product: item.product,
        currentPercentage: item.currentPercentage,
        targetPercentage: item.targetPercentage,
      })),
    };
  }

  logDebugInfo() {
    console.table(this.getDebugInfo());
  }
}

// Auto-initialize when included
if (typeof window !== 'undefined') {
  window.AtivosGraficoSyncSystem = AtivosGraficoSyncSystem;
}
